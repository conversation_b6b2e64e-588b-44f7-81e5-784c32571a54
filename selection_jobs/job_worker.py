import os
import sys
import json
import time
import logging
import requests
import tempfile
import subprocess
from datetime import datetime
import sqlite3
import re
import requests

# Get the parent directory of the current script
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

# Add it to sys.path
sys.path.append(parent_dir)

import  TestServices.test_youtube_service as yt
import  TestServices.test_yt_download_service as yt_down
from Sanatana_Service.db_utils import get_channel_email_by_channel_id

log_dir = os.path.join(os.path.dirname(__file__), 'logs')  # or 'selection_jobs'
os.makedirs(log_dir, exist_ok=True)

log_file = os.path.join(log_dir, 'worker.log')
# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("job_worker")

import sys
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from Utils.globals import determine_domains_file
GLOBAL_DOMAINS_FILE = determine_domains_file()

try:
    global_config_path = os.path.join(os.path.dirname(__file__), '..', GLOBAL_DOMAINS_FILE)
    with open(global_config_path, 'r') as f:
        global_config = json.load(f)

        SANATANA_FILES_LOCATION = global_config.get("SANATANA_FILES_LOCATION", "")
        SANATANA_SERVICE_DOMAIN = global_config.get("SANATANA_SERVICE_DOMAIN", "")

        if not SANATANA_FILES_LOCATION or not SANATANA_SERVICE_DOMAIN:
            raise ValueError("Missing required configuration values"
                            )
        os.makedirs(SANATANA_FILES_LOCATION, exist_ok=True)

        logger.info(f"Files will be stored in: {SANATANA_FILES_LOCATION}")
        logger.info(f"Main service domain: {SANATANA_SERVICE_DOMAIN}")

except Exception as e:
    logger.error(f"Failed to load configuration: {str(e)}")
    raise

class JobWorker:
    """Worker class to process upload selection jobs"""

    # Rushi: Keep
    def __init__(self, selection_id, selection_data, update_status_callback=None):
        """Initialize the worker"""
        self.sanatana_email = selection_data.get('sanatana_email')
        self.selection_id = selection_id
        self.selection_data = selection_data
        self.update_status_callback = update_status_callback
        self.temp_dir = None
        self.download_info = None
        self.video_info = None
        self.upload_results = {}
        self.download_folder = None

    # Rushi: Keep
    def get_timestamp_folder_name(self):
        """Generate a timestamp folder name that's compatible with both Unix/Linux and Windows"""
        return datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    # Rushi: Keep
    def setup_download_folder(self):
        if self.download_folder:
            return self.download_folder 
        try:
            username = self.sanatana_email
            if not username:
                raise ValueError("self.sanatana_email is not set.")

            # Call external service to add/get account
            response = requests.get(f"https://service.mediaverse.site/add_account?username={username}")
            response.raise_for_status()  # Raises HTTPError for bad responses

            data = response.json()
            linux_username = data.get("linux_username")
            if not linux_username:
                raise ValueError("Missing 'linux_username' in response.")

            job_folder = f"job_group_{self.selection_id}"
            path = f"/mnt/mediaverse_content/{linux_username}/{job_folder}"
            self.download_folder = path
            return self.download_folder

        except requests.RequestException as e:
            print(f"HTTP request failed: {e}")
        except ValueError as ve:
            print(f"Value error: {ve}")
        except Exception as ex:
            print(f"Unexpected error: {ex}")
    
        return None  # Return None on failure
    
    def save_to_job_group(self,description=None,title=None):
        try:
            download_path = self.setup_download_folder()
            if not download_path:
                raise ValueError("Download path is invalid or empty.")
            
            if not description:
                description = ""

            if not title:
                title = ""

            # Write description
            try:
                with open(f"{download_path}/description.txt", "w", encoding="utf-8") as desc_file:
                    desc_file.write(description)
            except Exception as e:
                print(f"Failed to write description.txt: {e}")
                return False

            # Write title
            try:
                with open(f"{download_path}/title.txt", "w", encoding="utf-8") as title_file:
                    title_file.write(title)
            except Exception as e:
                print(f"Failed to write title.txt: {e}")
                return False
            
            # Write selection_data
            try:
                with open(f"{download_path}/selection.json", "w", encoding="utf-8") as selection_file:
                    selection_file.write(json.dumps(self.selection_data))
            except Exception as e:
                print(f"Failed to write selection.json: {e}")
                return False
            
            # Create ready file
            try:
                with open(f"{download_path}/ready", "w", encoding="utf-8") as ready_file:
                    ready_file.write("ready")
                    return True
            except Exception as e:
                print(f"Failed to create ready file: {e}")
                return False

            
        except Exception as e:
            print(f"Unexpected error during metadata save: {e}")
            return False
        

    # Rushi: Keep
    def update_status(self, status, details=None):
        """Update job status"""
        if self.update_status_callback:
            self.update_status_callback(self.selection_id, status, details)
        else:
            logger.info(f"Job {self.selection_id} status: {status}")
            if details:
                logger.info(f"Details: {json.dumps(details)}")

    # Rushi: Keep
    def download_content(self):
        """Download content from source"""
        self.update_status("downloading", {"message": "Starting content download"})

        try:
            source = self.selection_data.get('source')

            if source == 'youtube':
                return self._download_from_youtube()
            elif source == 'gdrive':
                return self._download_from_gdrive()
            else:
                raise ValueError(f"Unsupported source: {source}")

        except Exception as e:
            logger.error(f"Download failed: {str(e)}")
            self.update_status("failed", {"message": f"Download failed: {str(e)}"})
            raise

    # Rushi: Keep
    def _download_from_youtube(self):
        """Download content from YouTube"""
        video_link = self.selection_data.get('videoLink')
        if not video_link:
            raise ValueError("No video link provided")

        # Create temp directory for download
        self.temp_dir = self.setup_download_folder()

        # Call YouTube download service
        try:
          #  print("Before getting test_vidoe_info")

            self.video_info = yt_down.test_video_info(video_link)

           # print(f"Video info: {json.dumps(self.video_info)}")

            self.download_info = yt_down.test_download(video_link, "video", "1080", self.temp_dir)

           # print(f"Downloaded content: {json.dumps(self.download_info)}")

            if not self.download_info.get('file_path'):
                raise ValueError("Download failed, no file path returned")

            self.update_status("download_complete", {
                "message": "Content downloaded successfully",
                "file_path": self.download_info.get('file_path'),
                "file_name": self.download_info.get('file_name')
            })

            return True

        except Exception as e:
            logger.error(f"YouTube download failed (Exception): {str(e)}")
            self.update_status("failed", {"message": f"YouTube download failed Exception): {str(e)}"})
            raise

    def _download_from_gdrive(self):
        """Download content from Google Drive"""
        # Implementation would go here
        # For now, just simulate a download
        self.temp_dir = tempfile.mkdtemp(prefix="job_worker_")
        time.sleep(2)  # Simulate download time

        self.download_info = {
            "file_path": os.path.join(self.temp_dir, "sample_video.mp4"),
            "file_name": "sample_video.mp4"
        }

        self.update_status("download_complete", {
            "message": "Content downloaded from Google Drive",
            "file_path": self.download_info.get('file_path'),
            "file_name": self.download_info.get('file_name')
        })

        return True

    # Rushi: Keep
    def upload_to_destinations(self):
        """Upload content to all destinations"""
        self.update_status("upload_started", {"message": "Starting uploads to destinations"})

        destinations = self.selection_data.get('destinations', [])
        if not destinations:
            self.update_status("upload_complete", {"message": "No destinations specified"})
            return True

        success_count = 0
        for destination in destinations:
            dest_id = destination.get('id')
            dest_name = destination.get('name')

            try:
               

                self.update_status("uploading", {
                    "message": f"Uploading to {dest_name}",
                    "destination": dest_id
                })

                # Process based on destination type
                if dest_id== 'youtube_video':
                    result = self._upload_to_youtube(destination)
                elif dest_id== 'youtube_shorts':
                    result = self._upload_to_youtube_shorts(destination)
                elif dest_id == 'facebook':
                    result = self._upload_to_facebook(destination)
                elif dest_id == 'instagram':
                    result = self._upload_to_instagram(destination)
                else:
                    logger.warning(f"Unsupported destination: {dest_id}")
                    continue

                if result:
                    success_count += 1
                    self.upload_results[dest_id] = result

            except Exception as e:
                logger.error(f"Upload to {dest_id} failed: {str(e)}")
                self.update_status("upload_failed", {
                    "message": f"Upload to {dest_name} failed: {str(e)}",
                    "destination": dest_id
                })
                return False

        if success_count == len(destinations):
            # Collect YouTube URLs from results for easy access
            youtube_links = {}
            for dest_id, result in self.upload_results.items():
                if dest_id.startswith('youtube') and result:
                    if 'youtube_link' in result:
                        youtube_links[dest_id] = {
                            'video_url': result['youtube_link'],
                            'title': result.get('title', 'YouTube Video')
                        }
                    if 'playlist_link' in result and result['playlist_link']:
                        youtube_links[dest_id]['playlist_url'] = result['playlist_link']

            self.update_status("upload_complete", {
                "message": "All uploads completed successfully",
                "results": self.upload_results,
                "youtube_links": youtube_links
            })
            return True
        elif success_count > 0:
            # Collect YouTube URLs from results for easy access
            youtube_links = {}
            for dest_id, result in self.upload_results.items():
                if dest_id.startswith('youtube') and result:
                    if 'youtube_link' in result:
                        youtube_links[dest_id] = {
                            'video_url': result['youtube_link'],
                            'title': result.get('title', 'YouTube Video')
                        }
                    if 'playlist_link' in result and result['playlist_link']:
                        youtube_links[dest_id]['playlist_url'] = result['playlist_link']

            self.update_status("upload_partial", {
                "message": f"{success_count} of {len(destinations)} uploads completed",
                "results": self.upload_results,
                "youtube_links": youtube_links
            })
            return True

    def _upload_to_youtube_shorts(self, destination):
        ...
    
    def _upload_to_generic_destination(self):
        # Retrieve title and description
        title = self._get_title()
        description = self._get_description()
        logger.info(f"generic_destination: Retrieved title: {title[:70]}")
        logger.info(f"generic_destination: Retrieved description: {description[:100]}...")  # Log first 100 chars
    
        # Truncate to max length
        description = description[:4500]
        title = title[:70]

        result = self.save_to_job_group(
            title=title,
            description=description 
        )
        if result:
            self.update_status("staged", {
                "message": "Job is staged for upload"
            })

        return result

    # Rushi: Keep
    def _upload_to_youtube(self, destination):
        """Upload to YouTube"""
        try:

            # Retrieve title and description
            title = self._get_title()
            description = self._get_description()
            logger.info(f"Retrieved title:  {title[:70]}")
            logger.info(f"Retrieved description: {description[:100]}...")  # Log first 100 chars
             # Truncate to max length
            description = description[:4500]
            title = title[:70]

            # Generate hashtags and keywords
            hashtags = self._get_hashtags()

            hash_keywords = hashtags

            desc_keywords = self.extract_hashtags(description)

            all_keywords = f"{desc_keywords}, {hash_keywords}"
            logger.info(f"Generated keywords: {all_keywords}")

           
            # Retrieve category and privacy status
            category = self.get_youtube_category_id(destination)
            privacy_status = destination.get('privacyStatus', 'public')  # Default privacy status
            logger.info(f"Category: {category}, Privacy Status: {privacy_status}")

            # Get playlist info
            playlist_info = destination.get('playlist', {})
            playlist_type = playlist_info.get('type', 'none')
            channel_id = playlist_info.get('channel_id')
            logger.info(f"Playlist Type: {playlist_type}, Channel ID: {channel_id}")
            file_path = self.download_info.get('file_path')
            logger.info(f"File path: {file_path}")

            
            if not channel_id:
                logger.error("No channel ID provided for YouTube upload")
                raise ValueError("No channel ID provided for YouTube upload")

            channel_email = get_channel_email_by_channel_id(channel_id)
            logger.info(f"Retrieved channel email: {channel_email}")

            if not channel_email:
                logger.error("No channel email found for YouTube upload")
                raise ValueError("No channel email found for YouTube upload")

            # Prepare upload request
            logger.info(f"Initiating upload to YouTube: {title}")
            video_id = None
            playlist_link = None

            upload_response = yt.test_upload_video(
                file=file_path,
                title=title,
                description=description,
                keywords=all_keywords,
                category=category,
                privacyStatus=privacy_status,
                email=channel_email,
            )

            if upload_response["status"] == "SUCCESS":
                video_id = upload_response["video_id"]
                youtube_link = upload_response["youtube_link"]
                logger.info(f"Video uploaded successfully! ID: {video_id}, Link: {youtube_link}")
            else:
                error_string = upload_response.get('error')
                logger.error(f"Upload failed: {error_string}")
                if "uploadLimitExceeded" in error_string:
                    print("\n\n_upload_to_youtube: Upload limit exceeded for single day. Please try again tomorrow.\n\n")
                    self.update_status("Youtube_uploadLimitExceeded", {
                        "message": "Upload failed: Upload limit exceeded for single day",
                        "retry": "Tomorrow",

                    })
                else:
                    self.update_status("Youtube_Error", {
                        "message": error_string,

                    })
                return None

            # If playlist is specified, add to playlist
            if playlist_type != 'none':
                playlist_name = playlist_info.get('name')
                logger.info(f"Adding video to playlist: {playlist_name}")

                add_playlist_response = yt.test_add_to_playlist(
                    video_id=video_id,
                    playlist=playlist_name,
                    email=channel_email,
                )

                if add_playlist_response["status"] == "SUCCESS":
                    playlist_id = add_playlist_response["playlist_id"]
                    playlist_link = f"https://www.youtube.com/watch?v={video_id}&list={playlist_id}"
                    logger.info(f"Added video {video_id} to playlist successfully! Playlist ID: {playlist_id}")
                else:
                    logger.error(f"Add to playlist failed: {add_playlist_response.get('error')}")
                    return None

                self.update_status("playlist_updated", {
                    "message": f"Added to playlist: {playlist_name}"
                })

                links_response = yt.test_update_playlist_links(
                    playlist_id=playlist_id,
                    email=channel_email,
                )

                if links_response["status"] == "SUCCESS":
                    logger.info(f"Updated playlist links successfully for Playlist ID: {playlist_id}")
                else:
                    logger.error(f"Update playlist links failed: {links_response.get('error')}")
                    return None

                self.update_status("playlist_links_updated", {
                    "message": f"Updated prev and next links in playlist: {playlist_name}"
                })

            return {
                "video_id": video_id,
                "title": title,
                "youtube_link": f"https://www.youtube.com/watch?v={video_id}",
                "playlist_link": playlist_link,
            }

        except Exception as e:
            logger.error(f"YouTube upload failed: {str(e)}")
            raise


    def _upload_to_facebook(self, destination):
        """Upload to Facebook"""
        # Implementation would go here
        # For now, just simulate an upload
        time.sleep(2)

        return {
            "post_id": f"fb_{int(time.time())}",
            "title": self._get_title()
        }

    def _upload_to_instagram(self, destination):
        """Upload to Instagram"""
        # Implementation would go here
        # For now, just simulate an upload
        time.sleep(2)

        return {
            "post_id": f"ig_{int(time.time())}",
            "title": self._get_title()
        }


    def extract_hashtags(self, text):
        if not text or len(text) == 0:
            return ""

        hashtags = re.findall(r'#\w+', text)
        hashtags = ','.join(hashtags)
        return self.extract_keywords_from_hashtags(hashtags)

    def extract_keywords_from_hashtags(self, text):
        if not text or len(text) == 0:
            return ""
        # Split by comma, semicolon, or any whitespace
        parts = re.split(r'[,\s;]+', text)
        # Remove '#' and keep non-empty parts
        keywords = [part.lstrip('#') for part in parts if part]
        return ','.join(keywords)

    def _get_title(self):
        """Get the title to use for the upload"""
        if self.selection_data.get('useYouTubeTitle') and self.video_info and 'title' in self.video_info:
            return self.video_info['title']
        elif self.selection_data.get('useAITitle'):
            # AI title generation would go here
            return f"AI Generated Title - {datetime.now().strftime('%Y-%m-%d')}"
        else:
            return self.selection_data.get('title', f"Untitled - {datetime.now().strftime('%Y-%m-%d')}")

    def _get_description(self):
        """Get the description to use for the upload"""
        if self.selection_data.get('useSourceDescription') and self.video_info and 'description' in self.video_info:
            return self.video_info['description']
        elif self.selection_data.get('useAIDescription'):
            # AI description generation would go here
            return f"AI Generated Description - {datetime.now().strftime('%Y-%m-%d')}"
        else:
            return self.selection_data.get('description', "")


    def get_youtube_category_id(self, destination):
        YOUTUBE_CATEGORIES = {
            "Film & Animation": 1,
            "Autos & Vehicles": 2,
            "Music": 10,
            "Pets & Animals": 15,
            "Sports": 17,
            "Short Movies": 18,
            "Travel & Events": 19,
            "Gaming": 20,
            "Videoblogging": 21,
            "People & Blogs": 22,
            "Comedy": 23,
            "Entertainment": 24,
            "News & Politics": 25,
            "Howto & Style": 26,
            "Education": 27,
            "Science & Technology": 28,
            "Nonprofits & Activism": 29,
            "Movies": 30,
            "Anime/Animation": 31,
            "Action/Adventure": 32,
            "Classics": 33,
            "Documentary": 35,
            "Drama": 36,
            "Family": 37,
            "Foreign": 38,
            "Horror": 39,
            "Sci-Fi/Fantasy": 40,
            "Thriller": 41,
            "Shorts": 42,
            "Shows": 43,
            "Trailers": 44
        }


        if "categories" in destination and isinstance(destination["categories"], list):
            category_name = destination["categories"][0]

            cat_id =  YOUTUBE_CATEGORIES.get(category_name)

            return cat_id

        return 22

    def _get_hashtags(self):
        """Get hashtags to use for the upload"""
        if self.selection_data.get('useAIHashtags'):
            # AI hashtag generation would go here
            return "#AI #Generated #Hashtags"
        else:
            hashtags = self.selection_data.get('hashtags', "")
            if isinstance(hashtags, list):
                hashtags = ",".join(map(str, hashtags)).replace("#", "")
            return hashtags

    def cleanup(self):
        """Clean up temporary files"""
        # if self.temp_dir and os.path.exists(self.temp_dir):
        #     try:
        #         import shutil
        #         shutil.rmtree(self.temp_dir)
        #         logger.info(f"Cleaned up temp directory: {self.temp_dir}")
        #     except Exception as e:
        #         logger.error(f"Error cleaning up temp directory: {str(e)}")
        pass
    
    def process(self):
        """Process the job from start to finish"""
        try:
            #Download content
            if not self.download_content():
                return False
            
            if not self._upload_to_generic_destination():
                 return False
            
            # Upload to destinations
            # if not self.upload_to_destinations():
            #     return False

            # # Mark job as completed
            # self.update_status("completed", {
            #     "message": "Job completed successfully",
            #     "results": self.upload_results
            # })

            return True

        except Exception as e:
            logger.error(f"Job processing threw exception: {str(e)}")
            self.update_status("failed", {"message": f"Job processing failed (Exception): {str(e)}"})
            return False

        finally:
            # Clean up
            self.cleanup()

# Function to process a job
def process_selection_job(selection_id, selection_data, update_status_callback=None):
    """Process a selection job"""
    worker = JobWorker(selection_id, selection_data, update_status_callback)
    return worker.process()

if __name__ == "__main__":
    # Test with a sample job
    sample_selection_string = """
    {
        "sanatana_email": "<EMAIL>",

	"source": "youtube",
	"sourceName": "YouTube",
	"destinations": [
		{
			"id": "youtube_video",
			"name": "YouTube Video",
			"playlist": {
				"channel_name": "Rushi Narasimha",
				"channel_id": "UCJ5cX0hQACcLwtTlyPyWj9g",
				"type": "none"
			},
			"categories": [
				"Howto & Style"
			]
		}
	],
	"title": null,
	"description": null,
	"extractFromFolder": false,
	"scheduleDate": "2025-04-08T20:32:24.956Z",
	"scheduleTime": null,
	"videoLink": "https://www.youtube.com/watch?v=XoX0g5oenDI",
	"hashtags": [
		"notebooklm podcast",
		"elevenlabs",
		"genfm",
		"elevenlabs voice cloning",
		"elevenlabs podcast"
	],
	"categories": [
		"Howto & Style"
	],
	"useYouTubeTitle": true,
	"useAITitle": false,
	"useCustomTitle": false,
	"useSourceDescription": true,
	"useAIDescription": false,
	"useCustomDescription": false,
	"useAIHashtags": false,
	"timeZone": "America/Chicago",
	"uploadWithinHour": true
    }


    """
    sample_selection = json.loads(sample_selection_string)
    def test_status_callback(selection_id, status, details):
        print("-"*60)
        print(f"Job {selection_id} status: {status}")
        if details:
            print(f"Details: {json.dumps(details, indent=2)}")

    process_selection_job(999, sample_selection, test_status_callback)

