from flask import Flask, request, jsonify
from root_app import app
from db import DATABASE

import sqlite3


from datetime import datetime, timezone


def get_db_connection():
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/set_download_data', methods=['POST'])
def set_download_data():
    data = request.json
    if 'url' not in data:
        return jsonify({'error': 'Missing "url" in payload'}), 400

    url = data['url']
    update_time = datetime.now(timezone.utc).isoformat()
    fields = {k: v for k, v in data.items() if k != 'url'}

    if not fields:
        return jsonify({'error': 'No fields to update or insert'}), 400

    conn = get_db_connection()
    cursor = conn.cursor()

    # Check if the URL already exists
    cursor.execute("SELECT 1 FROM downloads WHERE url = ?", (url,))
    exists = cursor.fetchone()

    if exists:
        # Build dynamic update statement
        field_names = list(fields.keys())
        set_clause = ", ".join([f"{key} = ?" for key in field_names])
        values = [fields[key] for key in field_names]
        values.append(update_time)
        values.append(url)
        query = f"UPDATE downloads SET {set_clause}, updated_at = ? WHERE url = ?"
        cursor.execute(query, values)
    else:
        # Insert new row
        insert_fields = list(fields.keys()) + ['url', 'updated_at']
        placeholders = ', '.join(['?'] * len(insert_fields))
        query = f"INSERT INTO downloads ({', '.join(insert_fields)}) VALUES ({placeholders})"
        values = [fields.get(field) for field in fields] + [url, update_time]
        cursor.execute(query, values)

    conn.commit()
    cursor.close()
    conn.close()

    return jsonify({'status': 'success', 'updated': bool(exists)}), 200

@app.route('/get_download_data', methods=['GET'])
def get_download_data():
    args = request.args
    search_fields = ['id', 'url', 'download_path']
    filters = [(key, args.get(key)) for key in search_fields if args.get(key) is not None]

    if not filters:
        return jsonify({'error': 'Provide at least one of: id, url, or download_path'}), 400

    # Use only the first provided field for lookup
    field, value = filters[0]

    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute(f"SELECT * FROM downloads WHERE {field} = ? LIMIT 1", (value,))
    row = cursor.fetchone()
    cursor.close()
    conn.close()

    if row:
        return jsonify(dict(row)), 200
    else:
        return jsonify({'error': 'No matching download data found'}), 404
