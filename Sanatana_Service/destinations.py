from flask import Flask, request, jsonify
from root_app import app
from db import DATABASE

import os
import sqlite3
import requests
import time
import json
import urllib.parse
import datetime


@app.route("/get_destinations", methods=["GET"])
def get_destinations():
    """
    Get destinations for a user based on sanatana_email.
    
    Query Parameters:
    - sanatana_email: User's email address
    
    Returns:
    JSON list of destinations for the user
    """
    try:
        sanatana_email = request.args.get("sanatana_email")
        
        if not sanatana_email:
            return jsonify({"error": "sanatana_email parameter is required"}), 400
        
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        cursor.execute(
            """
            SELECT id, label, destination_json, updated_at
            FROM destinations
            WHERE sanatana_email = ? COLLATE NOCASE
            ORDER BY label ASC
            """,
            (sanatana_email,)
        )
        
        rows = cursor.fetchall()
        
        destinations = []
        for row in rows:
            destinations.append({
                "id": row[0],
                "label": row[1],
                "destination_json": json.loads(row[2]) if row[2] else [],
                "updated_at": row[3]
            })
        
        conn.close()
        
        return jsonify(destinations), 200
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/set_destination", methods=["POST"])
def set_destination():
    """
    Set or update a destination for a user.
    
    Expected JSON payload:
    {
        "sanatana_email": "<EMAIL>",
        "label": "My Destination Label",
        "destination_json": [destination objects array]
    }
    
    Returns:
    Success message or error
    """
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ["sanatana_email", "label", "destination_json"]
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        sanatana_email = data["sanatana_email"]
        label = data["label"]
        destination_json = data["destination_json"]
        
        # Convert destination_json to string if it's not already
        if isinstance(destination_json, (list, dict)):
            destination_json_str = json.dumps(destination_json)
        else:
            destination_json_str = destination_json
        
        current_timestamp = datetime.datetime.now().isoformat()
        
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        # Check if the label and sanatana_email combination already exists (case insensitive)
        cursor.execute(
            """
            SELECT id FROM destinations
            WHERE sanatana_email = ? COLLATE NOCASE AND label = ? COLLATE NOCASE
            """,
            (sanatana_email, label)
        )
        
        existing_record = cursor.fetchone()
        
        if existing_record:
            # Update existing record
            cursor.execute(
                """
                UPDATE destinations
                SET destination_json = ?, updated_at = ?
                WHERE sanatana_email = ? COLLATE NOCASE AND label = ? COLLATE NOCASE
                """,
                (destination_json_str, current_timestamp, sanatana_email, label)
            )
            message = "Destination updated successfully"
        else:
            # Insert new record
            cursor.execute(
                """
                INSERT INTO destinations (sanatana_email, label, destination_json, updated_at)
                VALUES (?, ?, ?, ?)
                """,
                (sanatana_email, label, destination_json_str, current_timestamp)
            )
            message = "Destination created successfully"
        
        conn.commit()
        conn.close()
        
        return jsonify({"message": message}), 200
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500
