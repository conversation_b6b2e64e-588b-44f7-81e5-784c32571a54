from flask import Flask, request, jsonify
from root_app import app
from db import DATABASE

import os
import sqlite3
import requests
import time
import json
import urllib.parse

from datetime import datetime, timedelta, timezone
import pytz
import math


@app.route('/get_destinations/<sanatana_email>', methods=['GET'])
def get_destinations(sanatana_email):
    try:
        # Load destinations.json file
        destinations_path = os.path.join(os.path.dirname(__file__), 'assets', 'destinations.json')
        with open(destinations_path, 'r') as f:
            destinations = json.load(f)

      
        # # Process each destination
        # for dest in destinations:
        #     dest_id = dest.get('id', '')

        #     dest['authorized'] = False
            
        #     # Extract platform from id (text before underscore if one exists)
        #     platform = dest_id.split('_')[0] if '_' in dest_id else dest_id
            
        #     # Query credentials table for this platform and email
        #     cursor.execute(
        #         "SELECT client_id FROM credentials WHERE sanatana_email = ? AND platform = ?",
        #         (sanatana_email, platform)
        #     )
        #     cred_results = cursor.fetchall()

        #     # Loop through all matching credentials
        #     for cred_row in cred_results:
        #         client_id = cred_row[0]

        #         # Query users table to check if user is authorized
        #         cursor.execute(
        #             "SELECT * FROM users WHERE sanatana_email = ? AND client_id = ?",
        #             (sanatana_email, client_id)
        #         )
        #         user_result = cursor.fetchone()

        #         # If user record exists, set authorized to true and exit the loop
        #         if user_result:
        #             dest['authorized'] = True
        #             break  # Exit the loop once we find an authorized credential

          
        url = "https://service.mediaverse.site/get_folder_run_users"
        params = {"email": sanatana_email}

        try:
            response = requests.get(url, params=params)
        except requests.RequestException as e:
            return jsonify({'error': f"Failed to reach MediaVerse service: {str(e)}"}), 502

        if response.status_code != 200:
            return jsonify({
                'error': f"MediaVerse service returned status {response.status_code}"
            }), response.status_code

        try:
            data = response.json()
        except ValueError as e:
            return jsonify({'error': 'Invalid JSON received from MediaVerse service'}), 502

        # Handle case where MediaVerse returns a list directly instead of a dict
        if isinstance(data, list):
            folder_run_users = data
        else:
            if "error" in data:
                return jsonify({'error': f"MediaVerse error: {data['error']}"}), 502
            folder_run_users = data.get("folder_run_users", [])

        # Process each destination
        for dest in destinations:
            dest_id = dest.get('id', '')
            dest['authorized'] = False

            # Extract platform from ID
            platform = dest_id.split('_')[0] if '_' in dest_id else dest_id

            # Check if any user matches platform (case-insensitive) and is active
            for user in folder_run_users:
                if (str(user.get("platform", "")).lower() == platform.lower() and
                        int(user.get("active", 0)) == 1):
                    dest['authorized'] = True
                    break  # No need to check more users once authorized
                
        return jsonify(destinations)

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/authorized_profiles/<sanatana_email>', methods=['GET'])
def get_authorized_profiles(sanatana_email):
    try:
        url = "https://service.mediaverse.site/get_folder_run_users"
        params = {"email": sanatana_email}

        try:
            response = requests.get(url, params=params)
        except requests.RequestException as e:
            return jsonify({'error': f"Failed to reach MediaVerse service: {str(e)}"}), 502

        if response.status_code != 200:
            return jsonify({
                'error': f"MediaVerse service returned status {response.status_code}"
            }), response.status_code

        try:
            data = response.json()
        except ValueError as e:
            return jsonify({'error': 'Invalid JSON received from MediaVerse service'}), 502

        # Handle case where MediaVerse returns a list directly instead of a dict
        if isinstance(data, list):
            folder_run_users = data
        else:
            if "error" in data:
                return jsonify({'error': f"MediaVerse error: {data['error']}"}), 502
            folder_run_users = data.get("folder_run_users", [])

        return jsonify(folder_run_users)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

