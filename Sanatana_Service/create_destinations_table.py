#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create the destinations table in the database.
This table stores labeled destination configurations for users.
"""

import sqlite3
import os
import sys

# Add parent directory to path to import db module
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)

from Sanatana_Service.db import DAT<PERSON>AS<PERSON>

def create_destinations_table():
    """Create the destinations table if it doesn't exist."""
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        # Create the destinations table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS destinations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sanatana_email TEXT NOT NULL COLLATE NOCASE,
                label TEXT NOT NULL COLLATE NOCASE,
                destination_json TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                UNIQUE(sanatana_email, label)
            )
        """)
        
        # Create index for faster lookups
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_destinations_email_label 
            ON destinations(sanatana_email, label)
        """)
        
        conn.commit()
        print("✓ Destinations table created successfully")
        
        # Check if table was created
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='destinations'")
        if cursor.fetchone():
            print("✓ Table 'destinations' exists in database")
        else:
            print("✗ Failed to create table 'destinations'")
            
        conn.close()
        
    except Exception as e:
        print(f"✗ Error creating destinations table: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    print(f"Creating destinations table in database: {DATABASE}")
    success = create_destinations_table()
    if success:
        print("✓ Database setup completed successfully")
    else:
        print("✗ Database setup failed")
        sys.exit(1)
