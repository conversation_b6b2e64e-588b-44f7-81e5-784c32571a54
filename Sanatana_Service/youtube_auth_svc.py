from flask import Flask, request, jsonify
from root_app import app
from db import DATABASE

import os
import sqlite3
import requests
import time
import json
import urllib.parse

from datetime import datetime, timedelta, timezone
import pytz
import math
from flask import render_template_string
from yt_account import check_long_video_status, update_long_uploads_status


import sys
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from Utils.globals import determine_domains_file
GLOBAL_DOMAINS_FILE = determine_domains_file()

# Load SANATANA_SERVICE_DOMAIN from global ports file
global_config_path = os.path.join(
    os.path.dirname(__file__), "..", GLOBAL_DOMAINS_FILE
)
with open(global_config_path, "r") as f:
    global_config = json.load(f)

SANATANA_SERVICE_DOMAIN = global_config.get("SANATANA_SERVICE_DOMAIN", "")

AUTHORIZATION_URL = "https://accounts.google.com/o/oauth2/auth"
TOKEN_URL = "https://oauth2.googleapis.com/token"
SCOPE = "https://www.googleapis.com/auth/youtube https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile"


# Generate Authorization URL
@app.route("/hello")
def hello():
    return "Hellow, There! How are you?"


# Handle OAuth Callback
@app.route("/youtube/oauth2callback")
def oauth2callback():
    code = request.args.get("code")
    state = request.args.get("state")

    if not code or not state:
        return jsonify(
            {"error": "Authorization failed. No code and state received."}
        ), 400

    decoded_state = urllib.parse.unquote(state)
    state_data = json.loads(decoded_state)
    sanatana_email = state_data.get("sanatana_email")
    id = state_data.get("id")
    trial = state_data.get("trial")

    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute(
        """SELECT client_id, client_secret FROM credentials WHERE id = ?""", (id,)
    )
    row = cursor.fetchone()
    cursor.close()
    conn.close()

    if not row:
        return jsonify({"error": "No matching credentials found"}), 404

    CLIENT_ID, CLIENT_SECRET = row
    REDIRECT_URI = f"{SANATANA_SERVICE_DOMAIN}/youtube/oauth2callback"

    # Exchange authorization code for tokens
    data = {
        "code": code,
        "client_id": CLIENT_ID,
        "client_secret": CLIENT_SECRET,
        "redirect_uri": REDIRECT_URI,
        "grant_type": "authorization_code",
    }
    response = requests.post(TOKEN_URL, data=data)
    token_data = response.json()

    if "access_token" not in token_data:
        print(f"ERROR: Token exchange failed. Response: {token_data}")  # Debugging info
        return jsonify(
            {"error": "Failed to retrieve access token", "details": token_data}
        ), 400

    access_token = token_data["access_token"]
    refresh_token = token_data.get("refresh_token")
    expires_in = token_data.get("expires_in", 3600)
    token_expiry = int(time.time()) + expires_in

    # Fetch user's email
    channel_info = get_channel_info(access_token)
    if not channel_info:
        return jsonify({"error": "Failed to retrieve user email"}), 400
    channel_email = channel_info["email"]

    client_id = CLIENT_ID

    # Store tokens
    save_channel_tokens(
        sanatana_email,
        channel_email,
        client_id,
        access_token,
        refresh_token,
        token_expiry,
    )

    # Fetch and store YouTube channels
    store_youtube_channels(channel_email, access_token)

    insert_daily_quota(client_id, "youtube")

    return auth_success_html()

def auth_success_html(): 
    html = """
      <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Authorization Successful</title>
    <style>
        body {
            background: linear-gradient(to right, #f9f7f7, #dbe2ef);
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            flex-direction: column;
            color: #112d4e;
            text-align: center;
        }

        img {
            width: 160px;
            height: 160px;
            object-fit: cover;
            border-radius: 50%;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        }

        .spinner {
            margin: 10px auto;
            width: 40px;
            height: 40px;
            border: 4px solid #3f72af;
            border-top: 4px solid #f9f7f7;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .checkmark {
            display: none;
            font-size: 2.5rem;
            color: #3ec70b;
            margin-top: 15px;
        }

        footer {
            position: absolute;
            bottom: 20px;
            font-size: 0.9rem;
            color: #3f72af;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <img src="/static/sanatana_logo.jpg" alt="Sanatana Logo">
    <h1>Authorization Successful</h1>
    <p>Refresh the app to see the new channel.</p>
    <p>Closing in <span id="countdown">3</span> seconds...</p>
    <div class="spinner" id="spinner"></div>
    <div class="checkmark" id="checkmark">✓ Done</div>

    <footer>Powered by Sanatana</footer>

    <script>
        let count = 3;
        const countdown = document.getElementById("countdown");
        const spinner = document.getElementById("spinner");
        const checkmark = document.getElementById("checkmark");

        const timer = setInterval(() => {
            count--;
            countdown.textContent = count;

            if (count === 0) {
                clearInterval(timer);
                spinner.style.display = "none";
                checkmark.style.display = "block";
                setTimeout(() => window.close(), 800);  // Short pause before closing
            }
        }, 1000);
    </script>
</body>
</html>
        """

    return render_template_string(html)

# Refresh Youtube Access Token
@app.route("/youtube/refresh_token/<channel_email>")
def refresh_access_token_endpoint(channel_email):
    try:
        needed_quota = request.args.get("needed_quota", type=int)

        if needed_quota is None:
            print("refresh_access_token_endpoint: needed_quota not passed in")
            return jsonify(
                {"error": "refresh_access_token_endpoint:needed_quota is required"}
            ), 400
        return refresh_access_token(channel_email, needed_quota)

    except Exception as e:
        print(f"refresh_access_token_endpoint: EXCEPTION: {e}")
        return jsonify({"error": f"refresh_access_token_endpoint: Exception: {e}"})


def refresh_access_token(channel_email, needed_quota):
    """Retrieve or refresh the user's access token and list available YouTube channels."""
    # print(f"\n[DEBUG] refresh_access_token: Starting for channel_email={channel_email}, needed_quota={needed_quota}")
    try:
        # print(f"[DEBUG] refresh_access_token: Calling get_access_tokens_for_quota for {channel_email}")
        ret_obj = get_access_tokens_for_quota(channel_email, needed_quota)

        # print(f"[DEBUG] refresh_access_token: get_access_tokens_for_quota returned: {ret_obj and ret_obj.get('status')}")

        if not ret_obj:
            print(
                f"[ERROR] refresh_access_token: get_access_tokens_for_quota FAILED. ret_obj: {ret_obj}"
            )
            return jsonify({"status": "FAILED", "error": "User not found", "data": ret_obj}), 404

        if ret_obj["status"] != "SUCCESS":
            print(
                f"[ERROR] refresh_access_token: access token based on quota failed: {ret_obj}"
            )
            return jsonify({"status": "FAILED", "error": "Failed to retrieve access token", "data": ret_obj})

        # print(f"[DEBUG] refresh_access_token: Successfully got token data from get_access_tokens_for_quota")

        access_token = ret_obj["access_token"]
        refresh_token = ret_obj["refresh_token"]
        token_expiry = ret_obj["token_expiry"]
        client_id = ret_obj["client_id"]
        sanatana_email = ret_obj["sanatana_email"]

        # print(f"[DEBUG] refresh_access_token: Got token data - client_id={client_id}, sanatana_email={sanatana_email}")
        # print(f"[DEBUG] refresh_access_token: Token expires at {token_expiry}, current time: {int(time.time())}")

        # If access token is still valid, return it
        if access_token and int(time.time()) < token_expiry:
            # print(f"[DEBUG] refresh_access_token: Access Token is VALID (not expired)")
            # print(f"[DEBUG] refresh_access_token: Fetching channels for {channel_email}")
            channels = get_user_channels(channel_email)
            # print(f"[DEBUG] refresh_access_token: Found {len(channels)} channels")

            return jsonify(
                {
                    "status": "SUCCESS",
                    "access_token": access_token,
                    "client_id": client_id,
                    "sanatana_email": sanatana_email,
                    "channels": channels,
                }
            )

        # print(f"[DEBUG] refresh_access_token: Token EXPIRED, need to refresh it")
        # print(f"[DEBUG] refresh_access_token: Getting client_secret for client_id={client_id}")

        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        cursor.execute(
            """SELECT client_id, client_secret FROM credentials WHERE client_id = ?""",
            (client_id,),
        )
        row = cursor.fetchone()
        cursor.close()
        conn.close()

        if not row:
            print(
                f"[ERROR] refresh_access_token: No Client ID found in credentials table for {client_id}"
            )
            return jsonify({"status": "FAILED", "error": "No matching credentials found"}), 404

        # print(f"[DEBUG] refresh_access_token: Found credentials, now refreshing token")

        CLIENT_ID, CLIENT_SECRET = row
        # Refresh the token
        data = {
            "client_id": CLIENT_ID,
            "client_secret": CLIENT_SECRET,
            "refresh_token": refresh_token,
            "grant_type": "refresh_token",
        }
        # print(f"[DEBUG] refresh_access_token: Posting to {TOKEN_URL} to refresh token")
        response = requests.post(TOKEN_URL, data=data)
        token_data = response.json()
        # print(f"[DEBUG] refresh_access_token: Token refresh response status: {response.status_code}")

        if "access_token" not in token_data:
            print(
                f"[ERROR] refresh_access_token: Failed to retrieve access token. Response: {token_data}"
            )
            return jsonify(
                {"status": "FAILED", "error": "Failed to refresh access token", "details": token_data}
            ), 400

        new_access_token = token_data["access_token"]
        expires_in = token_data.get("expires_in", 3600)
        new_expiry_time = int(time.time()) + expires_in
        # print(f"[DEBUG] refresh_access_token: Got new access token, expires in {expires_in} seconds")

        # print(f"[DEBUG] refresh_access_token: Saving new tokens for {channel_email}")
        save_channel_tokens(
            sanatana_email,
            channel_email,
            client_id,
            new_access_token,
            refresh_token,
            new_expiry_time,
        )

        # print(f"[DEBUG] refresh_access_token: Fetching channels for {channel_email}")
        channels = get_user_channels(channel_email)
        # print(f"[DEBUG] refresh_access_token: Found {len(channels)} channels")

        # print(f"[DEBUG] refresh_access_token: Successfully refreshed token for {channel_email}")
        return jsonify(
            {
                "status": "SUCCESS",
                "access_token": new_access_token,
                "client_id": client_id,
                "sanatana_email": sanatana_email,
                "channels": channels,
            }
        )

    except Exception as e:
        print(f"[ERROR] refresh_access_token: EXCEPTION: {e}")
        import traceback

        traceback.print_exc()
        return jsonify({"status": "FAILED", "error": f"refresh_access_token Exception: {e}"})


@app.route("/youtube/upload_user_creds", methods=["POST"])
def upload_user_creds():
    if "creds" not in request.files:
        return jsonify({"error": "No file part"}), 400

    file = request.files["creds"]
    data = request.form.to_dict()
    required_fields = ["sanatana_email", "for_use", "platform"]

    # Check if all required fields are provided
    if not all(field in data for field in required_fields):
        return jsonify({"error": "Missing required fields"}), 400

    if file.filename == "":
        return jsonify({"error": "No selected file"}), 400

    # Read file content
    creds_content = file.read().decode("utf-8")

    # Insert into database
    insert_youtube_credentials(
        data["sanatana_email"], data["for_use"], data["platform"], creds_content
    )

    return jsonify(
        {
            "message": "File uploaded and stored successfully",
            "sanatana_email": data["sanatana_email"],
            "for_use": data["for_use"],
            "platform": data["platform"],
        }
    )


def get_channels(sanatana_email, client_id):
    """Returns an array of channel objects containing channel_name, youtube_link, and longUploadsStatus."""

    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    try:
        # Get list of emails from users table for the given sanatana_email and client_id
        cursor.execute(
            "SELECT email FROM users WHERE sanatana_email = ? AND client_id = ?",
            (sanatana_email, client_id),
        )
        emails = [row[0] for row in cursor.fetchall()]

        if not emails:
            print(
                f"get_channels: No emails found for the given {sanatana_email} and {client_id}"
            )
            return []

        # Prepare list for storing channel objects
        channel_list = []

        # Query user_channels for channels matching these emails
        cursor.execute(
            "SELECT channel_name, channel_id, longUploadsStatus, email FROM user_channels WHERE email IN ({seq})".format(
                seq=",".join(["?"] * len(emails))
            ),
            emails,
        )

        # Construct the channel objects
        for channel_name, channel_id, longUploadsStatus, channel_email in cursor.fetchall():
            channel_list.append(
                {
                    "channel_name": channel_name,
                    "channel_email": channel_email,
                    "youtube_link": f"https://www.youtube.com/channel/{channel_id}",
                    "longUploadsStatus": longUploadsStatus,
                }
            )

        return channel_list

    except sqlite3.Error as e:
        print(f"get_channels: Database error: {e}")
        return []

    finally:
        cursor.close()
        conn.close()


def get_available_uploads(client_id):
    """Returns the number of videos that can be uploaded based on available quota."""

    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    try:
        # Get quota_limit and quota_used from daily_quotas
        cursor.execute(
            "SELECT quota_limit, quota_used FROM daily_quotas WHERE client_id = ?",
            (client_id,),
        )
        result = cursor.fetchone()

        if result is None:
            print(f"get_available_videos: {client_id} not found")
            return 0

        quota_limit, quota_used = result

        # Calculate available quota
        available_quota = quota_limit - quota_used

        # Calculate number of videos (each video costs 1650 quota)
        num_videos = math.floor(available_quota / 1650)

        return max(0, num_videos)  # Ensure it never returns a negative value

    except sqlite3.Error as e:
        print(f"get_available_videos: Database error: {e}")
        return 0

    finally:
        cursor.close()
        conn.close()


@app.route("/youtube/get_authorization_urls", methods=["POST"])
def get_authorization_urls():
    data = request.get_json()
    sanatana_email = data.get("sanatana_email")
    if not sanatana_email:
        return jsonify({"error": "Missing sanatana_email parameter"}), 400

    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute(
        '''SELECT id, project_id, client_id FROM credentials WHERE sanatana_email = ? AND platform = "youtube"''',
        (sanatana_email,),
    )
    rows = cursor.fetchall()
    cursor.close()
    conn.close()

    authorization_data = []
    for row in rows:
        id, project_id, client_id = row
        session_data = json.dumps(
            {"sanatana_email": sanatana_email, "id": id, "trial": "no"}
        )
        encoded_session_data = urllib.parse.quote(session_data)

        auth_url = (
            f"{AUTHORIZATION_URL}?client_id={client_id}"
            f"&redirect_uri={SANATANA_SERVICE_DOMAIN}/youtube/oauth2callback"
            f"&scope={SCOPE}&response_type=code"
            f"&access_type=offline&prompt=consent&state={encoded_session_data}"
        )

        channels = get_channels(sanatana_email, client_id)
        for channel in channels:
            channel_email = channel["channel_email"]
            
            # print(f"[DEBUG] get_authorized_channels: Processing channel {channel_name} (ID: {channel_id})")
            longUploadsStatus = channel.get("longUploadsStatus", "unknown")   
            #print("longUploadsStatus from channel", longUploadsStatus)
            if(longUploadsStatus != "eligible"):
                    if check_long_video_status(channel_email) == True:
                        longUploadsStatus = "eligible"
                        update_long_uploads_status(channel_email)
                        channel["longUploadsStatus"] = longUploadsStatus
        
        available_uploads = get_available_uploads(client_id)

        authorization_data.append(
            {
                "project_id": project_id,
                "authorization_url": auth_url,
                "channels": channels,
                "available_uploads": available_uploads,
            }
        )

    return jsonify(authorization_data)



@app.route("/update_quota", methods=["POST"])
def update_quota_endpoint():
    """Handles the /update_quota POST request."""
    data = request.get_json()

    # Extract client_id and new_used_quota from the request JSON
    client_id = data.get("client_id")
    new_used_quota = data.get("new_used_quota")

    if not client_id or new_used_quota is None:
        return jsonify({"error": "Both client_id and new_used_quota are required"}), 400

    result = update_quota(client_id, new_used_quota)

    if isinstance(result, dict) and "error" in result:
        return jsonify(result), 500
    elif isinstance(result, tuple):
        return jsonify(result[0]), result[1]

    return jsonify(result)


def get_midnight_pst():
    """Returns today's 12:00 AM PST time as a timestamp string for SQLite."""

    # Get the current date in UTC
    utc_now = datetime.utcnow()

    # Convert to PST timezone
    pst = pytz.timezone("America/Los_Angeles")

    # Get today's date in PST and set time to 12:00 AM
    pst_midnight = datetime(
        utc_now.year, utc_now.month, utc_now.day, 0, 0, 0, tzinfo=pytz.utc
    ).astimezone(pst)

    # Format as SQLite TIMESTAMP string (YYYY-MM-DD HH:MM:SS)
    timestamp = pst_midnight.strftime("%Y-%m-%d %H:%M:%S")

    print("Todays 12 AM PST timestamp:", timestamp)

    return timestamp


def has_24_hours_passed(timestamp_str):
    """Returns True if 24 hours have passed since the given timestamp (PST time)."""

    # Define PST timezone
    pst = pytz.timezone("America/Los_Angeles")

    # Convert input timestamp string to datetime (assuming it's in PST)
    timestamp_pst = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")

    # Attach PST timezone info
    timestamp_pst = pst.localize(timestamp_pst)

    # Get current time in UTC and convert to PST
    current_pst = datetime.now(timezone.utc).astimezone(pst)

    # Check if 24 hours have passed
    has_passed = (current_pst - timestamp_pst) >= timedelta(hours=24)

    print(f"Has 24 passed since {timestamp_str}: {has_passed}")

    return has_passed


def update_quota(client_id, new_used_quota):
    """Updates the used quota for the given client_id."""

    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    try:
        # Get the current quota_used and since timestamp for the client_id
        cursor.execute(
            "SELECT quota_used, since FROM daily_quotas WHERE client_id = ?",
            (client_id,),
        )
        result = cursor.fetchone()

        if result is None:
            return "Client ID not found", 404  # Client ID does not exist

        current_used_quota, since_timestamp = result  # Extract values

        # Initialize variables before conditional statements
        new_used_total = current_used_quota  # Default to the current value
        new_since = since_timestamp  # Default to the existing timestamp

        if since_timestamp is None or has_24_hours_passed(since_timestamp):
            # Reset quota_used and update 'since' timestamp
            new_used_total = new_used_quota
            new_since = get_midnight_pst()
        else:
            # Add to existing quota_used, keep 'since' unchanged
            new_used_total = current_used_quota + new_used_quota

        # Update daily_quotas table
        cursor.execute(
            "UPDATE daily_quotas SET quota_used = ?, since = ? WHERE client_id = ?",
            (new_used_total, new_since, client_id),
        )

        conn.commit()

        return {
            "message": f"Quota for client_id {client_id} updated successfully",
            "new_used_total": new_used_total,
        }

    except sqlite3.Error as e:
        print(f"update_quota: Database error: {e}")
        return {"error": "Database error"}, 500

    finally:
        cursor.close()
        conn.close()

def find_available_client_id(cursor, client_ids, needed_quota):
    for client_id in client_ids:
        cursor.execute(
            "SELECT quota_limit, quota_used FROM daily_quotas WHERE client_id = ?",
            (client_id,),
        )
        quota_row = cursor.fetchone()
        if quota_row:
            available_quota = quota_row[0] - quota_row[1]
            if available_quota >= needed_quota:
                return client_id
    return None

def get_access_tokens_for_quota(channel_email, needed_quota):
    """Fetches an access token if the available quota meets the needed quota."""

    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    try:
        # Get all client_id values for the given email
        cursor.execute("SELECT client_id FROM users WHERE email = ?", (channel_email,))
        client_ids = [row[0] for row in cursor.fetchall()]

        if not client_ids:
            return {"status": "NO CHANNEL FOUND"}  # No clients found for the email

        # Check available quotas
        selected_client_id = None
        selected_client_id = find_available_client_id(cursor, client_ids, needed_quota)

        if not selected_client_id:
            # Reset quota_used for all clients
            cursor.executemany(
                "UPDATE daily_quotas SET quota_used = 0 WHERE client_id = ?",
                [(cid,) for cid in client_ids]
            )

            # Retry after reset
            selected_client_id = find_available_client_id(cursor, client_ids, needed_quota)

        if not selected_client_id:
            return {"status": "QUOTA EXCEEDED"}

        # Fetch access details for the selected client_id
        cursor.execute(
            "SELECT email, access_token, refresh_token, token_expiry, client_id, sanatana_email FROM users WHERE client_id = ? AND email = ?",
            (selected_client_id, channel_email),
        )
        user_data = cursor.fetchone()

        if user_data:
            return {
                "status": "SUCCESS",
                "channel_email": user_data[0],
                "access_token": user_data[1],
                "refresh_token": user_data[2],
                "token_expiry": user_data[3],
                "client_id": user_data[4],
                "sanatana_email": user_data[5],
            }

        return {
            "status": "WRONG PLACE"
        }  # Shouldn't reach here unless data is inconsistent

    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return {"status": f"Database EXCEPTION occured: {e}"}

    finally:
        cursor.close()
        conn.close()


def save_channel_tokens(
    sanatana_email, channel_email, client_id, access_token, refresh_token, token_expiry
):
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    cursor.execute(
        """
        SELECT * FROM users WHERE sanatana_email = ? AND email = ? AND client_id = ?
    """,
        (sanatana_email, channel_email, client_id),
    )
    existing_user = cursor.fetchone()

    if existing_user:
        cursor.execute(
            """
            UPDATE users SET access_token = ?, refresh_token = ?, token_expiry = ?
            WHERE sanatana_email = ? AND email = ? AND client_id = ?
        """,
            (
                access_token,
                refresh_token,
                token_expiry,
                sanatana_email,
                channel_email,
                client_id,
            ),
        )
    else:
        cursor.execute(
            """
            INSERT INTO users (sanatana_email, email, client_id, access_token, refresh_token, token_expiry)
            VALUES (?, ?, ?, ?, ?, ?)
        """,
            (
                sanatana_email,
                channel_email,
                client_id,
                access_token,
                refresh_token,
                token_expiry,
            ),
        )
    
    conn.commit()
    cursor.close()
    conn.close()


def get_channel_info(access_token):
    """Retrieve the channel_info and email from Google API using access_token."""
    response = requests.get(
        "https://www.googleapis.com/oauth2/v1/userinfo",
        headers={"Authorization": f"Bearer {access_token}"},
    )

    if response.status_code != 200:
        print(
            f"ERROR: Failed to fetch user info. Response: {response.json()}"
        )  # Debugging info
        return None

    channel_info = response.json()
    return channel_info if "email" in channel_info else None


def get_user_channels(channel_email):
    """Retrieve all channels linked to a user's email."""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute(
        "SELECT channel_id, channel_name, email FROM user_channels WHERE email = ?",
        (channel_email,),
    )
    channels = cursor.fetchall()
    cursor.close()
    conn.close()
    return [
        {
            "channel_id": ch[0],
            "channel_name": ch[1],
            "channel_email": ch[2],
        }
        for ch in channels
    ]


def store_youtube_channels(channel_email, access_token):
    """Fetch and store all YouTube channels linked to the user's account."""
    response = requests.get(
        "https://www.googleapis.com/youtube/v3/channels?part=id,snippet&mine=true",
        headers={"Authorization": f"Bearer {access_token}"},
    )

    if response.status_code != 200:
        print(f"ERROR: Failed to fetch YouTube channels for {channel_email}")
        return

    channel_data = response.json()
    if "items" not in channel_data or len(channel_data["items"]) == 0:
        print(f"⚠ No YouTube channels found for {channel_email}")
        return

    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    for channel in channel_data["items"]:
        channel_id = channel["id"]
        channel_name = channel["snippet"]["title"]

        # Check if channel is already stored
        cursor.execute(
            "SELECT * FROM user_channels WHERE email = ? AND channel_id = ?",
            (channel_email, channel_id),
        )
        existing_channel = cursor.fetchone()

        if not existing_channel:
            cursor.execute(
                "INSERT INTO user_channels (email, channel_id, channel_name) VALUES (?, ?, ?)",
                (channel_email, channel_id, channel_name),
            )
            print(
                f"Stored YouTube channel: {channel_name} ({channel_id}) for {channel_email}"
            )

    conn.commit()
    cursor.close()
    conn.close()


# Function to insert data into the database
def insert_youtube_credentials(sanatana_email, for_use, platform, creds_content):
    data = json.loads(creds_content)
    web_data = data.get("web", {})
    client_id = web_data.get("client_id")
    project_id = web_data.get("project_id")
    client_secret = web_data.get("client_secret")

    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    # Check if client_id exists
    cursor.execute("SELECT * FROM credentials WHERE client_id = ?", (client_id,))
    existing_record = cursor.fetchone()

    if existing_record:
        # Update if client_id exists
        cursor.execute(
            """
            UPDATE credentials
            SET project_id = ?, client_secret = ?
            WHERE client_id = ?
        """,
            (project_id, client_secret, client_id),
        )
        print("Updated existing record.")
    else:
        cursor.execute(
            """
        INSERT INTO credentials (sanatana_email, for_use, platform, creds,
        project_id,
        client_id,
        client_secret,
        updated)
        VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        """,
            (
                sanatana_email,
                for_use,
                platform,
                creds_content,
                project_id,
                client_id,
                client_secret,
            ),
        )

    conn.commit()
    cursor.close()
    conn.close()


def insert_daily_quota(client_id, platform):
    """Inserts a new row into daily_quotas if client_id does not exist."""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    try:
        # Check if the client_id already exists
        cursor.execute("SELECT 1 FROM daily_quotas WHERE client_id = ?", (client_id,))
        if cursor.fetchone() is None:
            # Fetch the limit for YouTube from platform_limits
            cursor.execute(
                "SELECT quota_limit FROM platform_limits WHERE platform = ?",
                (platform,),
            )

            limit_value = cursor.fetchone()

            if limit_value:
                limit_value = limit_value[0]  # Extract the limit value from tuple

                # Insert new row into daily_quotas
                cursor.execute(
                    "INSERT INTO daily_quotas (client_id, platform, quota_limit) VALUES (?, ?, ?)",
                    (client_id, platform, limit_value),
                )
                conn.commit()
                print(f"Inserted new quota for client_id: {client_id}")
            else:
                print("Error: No quota_limit found for platform 'youtube'.")
        else:
            print(f"Client ID {client_id} already exists in daily_quotas.")

    except sqlite3.Error as e:
        print(f"insert_daily_quota: Database error: {e}")

    finally:
        cursor.close()
        conn.close()


def get_playlists_for_channel(channel_id, access_token):
    """Fetch all playlists for a specific YouTube channel."""
    url = "https://www.googleapis.com/youtube/v3/playlists"
    params = {"part": "snippet", "channelId": channel_id, "maxResults": 50}
    headers = {"Authorization": f"Bearer {access_token}"}

    playlists = []
    next_page_token = None

    while True:
        if next_page_token:
            params["pageToken"] = next_page_token

        response = requests.get(url, params=params, headers=headers)

        if response.status_code != 200:
            print(f"ERROR: Failed to fetch playlists for channel {channel_id}")
            return playlists

        data = response.json()

        for item in data.get("items", []):
            playlists.append(
                {
                    "id": item["id"],
                    "name": item["snippet"]["title"],
                    "url": f"https://www.youtube.com/playlist?list={item['id']}",
                }
            )

        next_page_token = data.get("nextPageToken")
        if not next_page_token:
            break

    return playlists


@app.route("/youtube/authorized_channels/<sanatana_email>")
def get_authorized_channels(sanatana_email):
    """Get all authorized channels with their playlists for a Sanatana email."""
    print(f"\n[DEBUG] get_authorized_channels: Starting for sanatana_email={sanatana_email}")

    if not sanatana_email:
        print("[ERROR] get_authorized_channels: Missing sanatana_email parameter")
        return jsonify({"error": "sanatana_email is required"}), 400

    PLAYLIST_LIST_NEEDS_QUOTA = 1
    # print(f"[DEBUG] get_authorized_channels: Using quota={PLAYLIST_LIST_NEEDS_QUOTA}")

    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    try:
        # 1. For the given sanatana_email, get all the unique emails
        # print(f"[DEBUG] get_authorized_channels: Fetching emails for sanatana_email={sanatana_email}")
        cursor.execute(
            "SELECT DISTINCT email FROM users WHERE sanatana_email = ?",
            (sanatana_email,),
        )
        emails = [row[0] for row in cursor.fetchall()]

        # print(f"[DEBUG] get_authorized_channels: Found {len(emails)} emails: {emails}")

        if not emails:
            print(
                f"[WARN] get_authorized_channels: No emails found for sanatana_email={sanatana_email}"
            )
            return jsonify([])

        # 2. Create a list called channel_playlists
        channel_playlists = []

        # 3. For each email, call refresh_access_token
        for channel_email in emails:
            # print(f"\n[DEBUG] get_authorized_channels: Processing channel_email={channel_email}")
            # Call the existing refresh_access_token method
            # print(f"[DEBUG] get_authorized_channels: Calling refresh_access_token for {channel_email}")
            response = refresh_access_token(channel_email, PLAYLIST_LIST_NEEDS_QUOTA)

            # Check if response is valid - it's a Flask response object from jsonify()
            if isinstance(response, tuple) and len(response) > 1:
                # This is an error response with status code: (json_data, status_code)
                print(
                    f"[ERROR] get_authorized_channels: Error response from refresh_access_token: {response}"
                )
                continue

            # If it's a successful response, it will be a Response object with json data
            try:
                # Extract the JSON data from the response
                if hasattr(response, "get_data") and callable(response.get_data):
                    # It's a Flask Response object
                    import json

                    response_data = json.loads(response.get_data().decode("utf-8"))
                elif isinstance(response, dict):
                    # It's already a dictionary
                    response_data = response
                else:
                    print(
                        f"[ERROR] get_authorized_channels: Unknown response type: {type(response)}"
                    )
                    continue

                # Check if the required fields are present
                if "access_token" not in response_data:
                    print(
                        f"[ERROR] get_authorized_channels: No access_token in response: {response_data}"
                    )
                    continue

                access_token = response_data["access_token"]
                client_id = response_data["client_id"]
                # print("[DEBUG] get_authorized_channels: Successfully extracted access_token and client_id")

                # print(f"[DEBUG] get_authorized_channels: Got access_token and client_id={client_id}")

                channels = response_data.get("channels", [])
                # print(f"[DEBUG] get_authorized_channels: Found {len(channels)} channels for {channel_email}")

                # 4. Process each channel
                for channel in channels:
                    channel_id = channel["channel_id"]
                    channel_name = channel["channel_name"]
                    channel_email = channel["channel_email"]
                    
                    # print(f"[DEBUG] get_authorized_channels: Processing channel {channel_name} (ID: {channel_id})")
                    longUploadsStatus = channel.get("longUploadsStatus", "unknown")   
                    #print("longUploadsStatus from channel", longUploadsStatus)
                    if(longUploadsStatus != "eligible"):
                         if check_long_video_status(channel_email) == True:
                            longUploadsStatus = "eligible"
                            update_long_uploads_status(channel_email)

                    # Check if this channel is already in our list
                    existing_channel = next(
                        (c for c in channel_playlists if c["channel_id"] == channel_id),
                        None,
                    )

                    if existing_channel:
                        # print(f"[DEBUG] get_authorized_channels: Channel {channel_name} already in list, skipping")
                        pass
                    else:
                        # print(f"[DEBUG] get_authorized_channels: Fetching playlists for channel {channel_name}")
                        # Get playlists for this channel
                        playlists = get_playlists_for_channel(channel_id, access_token)
                        # print(f"[DEBUG] get_authorized_channels: Found {len(playlists)} playlists for channel {channel_name}")

                        # print(f"[DEBUG] get_authorized_channels: Updating quota for client_id={client_id}")
                        update_quota(client_id, PLAYLIST_LIST_NEEDS_QUOTA)

                        # 5. Create an object with channel details and playlists
                        channel_obj = {
                            "channel_id": channel_id,
                            "channel_name": channel_name,
                            "channel_email": channel_email,
                            "channel_url": f"https://www.youtube.com/channel/{channel_id}",
                            "playlists": playlists,
                            "longUploadsStatus": longUploadsStatus,
                        }

                        # Add to our list
                        channel_playlists.append(channel_obj)
                        # print(f"[DEBUG] get_authorized_channels: Added channel {channel_name} to result list")
            except Exception as e:
                print(
                    f"[ERROR] get_authorized_channels: Exception processing response: {e}"
                )
                continue
        # 6. Return the list of channels with playlists
        # print(f"\n[DEBUG] get_authorized_channels: Returning {len(channel_playlists)} channels with playlists")
        return jsonify(channel_playlists)

    except Exception as e:
        print(f"[ERROR] get_authorized_channels: Exception occurred: {e}")
        import traceback

        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

    finally:
        cursor.close()
        conn.close()


# Handle OAuth Callback
@app.route("/youtube/get_channel_email")
def get_channel_email():
    channel_id = request.args.get("channel_id")

    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        cursor.execute('SELECT email FROM user_channels WHERE channel_id = ?', (channel_id,))
        result = cursor.fetchone()
        
        cursor.close()
        conn.close()

        if result:
            return result[0]  # email
        else:
            return None  # Not found

    except sqlite3.Error as e:
        print(f"get_channel_email: Database error: {e}")
        return None