

from root_app import app
import os
import argparse
import sys

# Even though the below imports don't show as not used, they are used.
# They run the app endpoints.
import sanatana_app
import youtube_auth_svc 
import file_upload
import selection
import gdrive
import platforms
import downloads


def main():
    # Service name
    service_name = "SANATANA_SERVICE"

    # Parse command-line arguments for port
    parser = argparse.ArgumentParser(
        description=f"Start the {service_name} microservice."
    )
    parser.add_argument("--port", type=int, help="Port to run the service on")
    args = parser.parse_args()

    # Determine the port: Use CLI argument > Environment variable > Default
    PORT = args.port or int(os.getenv(service_name, 5000))

    # Get the parent directory of the current script
    parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

    # Add it to sys.path
    sys.path.append(parent_dir)

    from Utils.globals import save_port_in_global_ports

    # Write back to global ports file
    save_port_in_global_ports(service_name, PORT)

    print(f"Starting service {service_name} on port {PORT}...")
    app.run(host="0.0.0.0", port=PORT, threaded=True)
    # app.run(ssl_context=('C:/Dev/SocialMediaUpload/SSL/DefendingSanatanaDharma/fullchain.pem',
    #                  'C:/Dev/SocialMediaUpload/SSL/DefendingSanatanaDharma/private_key.key'),
    #         host='0.0.0.0', port=PORT)

    """
        usage:
        python sanatana_service.py --port 5000

        - Caution: this port is registered with Google Console Web Credentials. If you change it you
        must change there also

        - If the port is specified in command line it will use that port
        - If not, it will look for environment variable, SANATANA_SERVICE,
        - Even if that is not found, then it will assign default port to 5000
        - Saves port to global ports file under key SANATANA_SERVICE, so local client applications can connect


       To Authorize a Google User:
       From Local host:
       https://accounts.google.com/o/oauth2/auth?client_id=************-in4glnbah89opatflj1dfbk3087uhrlk.apps.googleusercontent.com&redirect_uri=http://127.0.0.1:5000/oauth2callback&scope=https://www.googleapis.com/auth/youtube https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile&response_type=code&access_type=offline&prompt=consent

       From External:
       https://accounts.google.com/o/oauth2/auth?client_id=************-in4glnbah89opatflj1dfbk3087uhrlk.apps.googleusercontent.com&redirect_uri=https://b7j82901-5000.use.devtunnels.ms/oauth2callback&scope=https://www.googleapis.com/auth/youtube https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile&response_type=code&access_type=offline&prompt=consent



       if something changes in scopes, to get the above url go to:
       http://127.0.0.1:5000/authorize
       https://b7j82901-5000.use.devtunnels.ms/authorize

    """


if __name__ == "__main__":
    main()
