from flask import Flask, request, jsonify
from root_app import app
from db import DATABASE

import os
import sqlite3
import requests
import time
import json
import urllib.parse

from datetime import datetime, timedelta, timezone
import pytz
import math


@app.route("/insert_sanatana_app_user", methods=["POST"])
def insert_sanatana_app_user():
    data = request.get_json()
    email = data.get("email")
    name = data.get("name")
    profile_picture = data.get("picture")
    refresh_token = data.get("refresh_token")
    access_token = data.get("access_token")

    if not email or not name or not profile_picture:
        return jsonify({"error": "Missing required fields"}), 400

    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    try:
        cursor.execute(
            """
        INSERT INTO Sanatana_users (email, name, profile_picture, refresh_token, access_token, signedin, last_login)
        VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ON CONFLICT(email) DO UPDATE SET
            name = excluded.name,
            profile_picture = excluded.profile_picture,
            refresh_token = excluded.refresh_token,
            access_token = excluded.access_token,
            signedin = 1,
            last_login = CURRENT_TIMESTAMP
    """,
            (email, name, profile_picture, refresh_token, access_token, 1),
        )  # Adding value for signedin

        conn.commit()
        return jsonify(
            {"message": "User inserted successfully (or already exists)"}
        ), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        cursor.close()
        conn.close()


@app.route("/get_sanatana_app_user", methods=["GET"])
def get_sanatana_app_user():
    """Fetch user details from Sanatana_users table by email."""
    email = request.args.get("email")
    if not email:
        return jsonify({"error": "Email parameter is required"}), 400

    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    # Query the user details
    cursor.execute(
        "SELECT email, name, profile_picture, refresh_token, access_token, signedin, last_login FROM Sanatana_users WHERE email = ?",
        (email,),
    )
    user = cursor.fetchone()
    cursor.close()
    conn.close()

    if user:
        user_data = {
            "email": user[0],
            "name": user[1],
            "profile_picture": user[2],
            "refresh_token": user[3],
            "access_token": user[4],
            "signedin": user[5],
            "last_login": user[6],
        }
        return jsonify(user_data)
    else:
        return jsonify({"error": "User not found"}), 404
