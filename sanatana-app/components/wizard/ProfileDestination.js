import React, { useState } from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import { Icon } from "@rneui/themed";

export default function ProfileDestination({
  dest,
  profileSelections,
  onSelectProfile,
  authorizedProfiles = [],
  isLoadingProfiles = false,
  excludeMode = false,
  onExcludeToggle = null,
  isExcluded = false,
  excludeSelections = {}
}) {
  const destinationId = dest.id;
  const platform = destinationId.split('_')[0];
  
  // Get profiles for this platform
  const platformProfiles = authorizedProfiles.filter(profile => 
    profile.platform?.toLowerCase() === platform.toLowerCase() && profile.active === 1
  );

  const currentSelection = profileSelections[destinationId] || [];

  const handleProfileToggle = (profileId) => {
    const newSelection = currentSelection.includes(profileId)
      ? currentSelection.filter(id => id !== profileId)
      : [...currentSelection, profileId];
    
    onSelectProfile(destinationId, newSelection);
  };

  if (isLoadingProfiles) {
    return (
      <View className="bg-gray-50 p-4 rounded-lg mt-3">
        <Text className="text-gray-600">Loading profiles...</Text>
      </View>
    );
  }

  if (platformProfiles.length === 0) {
    return (
      <View className="bg-gray-50 p-4 rounded-lg mt-3">
        <Text className="text-gray-600">No authorized profiles found for {platform}</Text>
      </View>
    );
  }

  return (
    <View className="bg-gray-50 p-4 rounded-lg mt-3">
      <View className="flex-row items-center justify-between mb-3">
        <Text className="text-lg font-semibold">
          {excludeMode ? "Exclude Profiles" : "Select Profiles"}
        </Text>
        {excludeMode && onExcludeToggle && (
          <TouchableOpacity
            onPress={() => onExcludeToggle(destinationId)}
            className="flex-row items-center"
          >
            <Icon
              name={isExcluded ? "check-square" : "square"}
              type="font-awesome"
              size={16}
              color={isExcluded ? "#3B82F6" : "#6B7280"}
            />
            <Text className="ml-2 text-sm text-gray-600">Exclude</Text>
          </TouchableOpacity>
        )}
      </View>

      <ScrollView className="max-h-48">
        {platformProfiles.map((profile) => {
          const isSelected = currentSelection.includes(profile.platform_userid);
          const profileKey = `${destinationId}_${profile.platform_userid}`;
          const isExcludedProfile = excludeSelections[profileKey] || false;

          return (
            <TouchableOpacity
              key={profile.platform_userid}
              onPress={() => {
                if (excludeMode) {
                  onExcludeToggle(destinationId, profile.platform_userid);
                } else {
                  handleProfileToggle(profile.platform_userid);
                }
              }}
              className={`flex-row items-center justify-between p-3 mb-2 rounded-lg ${
                excludeMode
                  ? (isExcludedProfile ? "bg-red-100 border border-red-300" : "bg-white border border-gray-200")
                  : (isSelected ? "bg-blue-100 border border-blue-300" : "bg-white border border-gray-200")
              }`}
            >
              <View className="flex-1">
                <Text className={`font-medium ${
                  excludeMode
                    ? (isExcludedProfile ? "text-red-800" : "text-gray-800")
                    : (isSelected ? "text-blue-800" : "text-gray-800")
                }`}>
                  {profile.username || profile.platform_userid}
                </Text>
                <Text className="text-sm text-gray-500">
                  ID: {profile.platform_userid}
                </Text>
              </View>

              <Icon
                name={excludeMode
                  ? (isExcludedProfile ? "times-circle" : "circle")
                  : (isSelected ? "check-circle" : "circle")
                }
                type="font-awesome"
                size={20}
                color={excludeMode
                  ? (isExcludedProfile ? "#DC2626" : "#D1D5DB")
                  : (isSelected ? "#3B82F6" : "#D1D5DB")
                }
              />
            </TouchableOpacity>
          );
        })}
      </ScrollView>

      {excludeMode && (
        <View className="mt-3 p-3 bg-yellow-50 rounded-lg">
          <Text className="text-sm text-yellow-800">
            Check "Exclude" to exclude selected profiles from upload when using "All Authorized Destinations"
          </Text>
        </View>
      )}
    </View>
  );
}
