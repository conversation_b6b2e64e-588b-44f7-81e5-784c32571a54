import React, { useState, useEffect, useContext } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  ActivityIndicator,
  Platform,
  Linking,
} from "react-native";
import { Icon } from "@rneui/themed";
import * as WebBrowser from "expo-web-browser";
import { Picker } from "@react-native-picker/picker";
import PropTypes from "prop-types";
import YoutubeDestination from "./YoutubeDestination";
import ProfileDestination from "./ProfileDestination";
import AuthorizationAlertModal from "./AuthorizationAlertModal";
import axios from "axios";
import Constants from "expo-constants";
import { UserContext } from "../../context/UserContext";
import {
  saveYoutubeSelections,
  loadYoutubeSelections,
  findMatchingChannel,
  createDefaultSelection,
  extractPlaylistId,
} from "../../utils/storageUtils";


// No longer need dummyPlaylists as we're using real data from the API

// New WebView component
function AuthorizationWebView({ url, onClose }) {
  const openBrowser = async () => {
    await WebBrowser.openBrowserAsync(url);
    onClose();
  };

  useEffect(() => {
    openBrowser();
  }, []);

  return null; // No need to render anything
}

// Add DestinationConfigurations
const DestinationConfigurations = [
  { id: "1", name: "Config1", configuration: {} },
  { id: "2", name: "Config2", configuration: {} },
  { id: "3", name: "Config3", configuration: {} },
];

export default function DestinationSelector({
  onComplete,
  initialDestinations,
  initialSelectedLabels,
  wizardData = {},
  buttonContainerStyle,
}) {
  const { user } = useContext(UserContext);
  const [destinations, setDestinations] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize with saved destinations if available (but not if labeled destinations are selected)
  const [selectedDestinations, setSelectedDestinations] = useState(() => {
    if (initialSelectedLabels && initialSelectedLabels.length > 0) {
      // If labeled destinations are selected, don't select individual destinations
      return [];
    }
    if (initialDestinations && initialDestinations.length > 0) {
      const destIds = initialDestinations.map((dest) => dest.id);
      // Remove duplicates while preserving order
      return [...new Set(destIds)];
    }
    return [];
  });

  // Initialize playlist selections from initial data
  const [playlistSelections, setPlaylistSelections] = useState(() => {
    if (initialDestinations && initialDestinations.length > 0) {
      const initialSelections = {};
      const playlistsByDest = {};

      // Group playlists by destination ID
      initialDestinations.forEach((dest) => {
        if (dest.playlist) {
          if (!playlistsByDest[dest.id]) {
            playlistsByDest[dest.id] = [];
          }
          playlistsByDest[dest.id].push(dest.playlist);
        }
      });

      // Set selections - use array if multiple, single object if one
      Object.keys(playlistsByDest).forEach(destId => {
        const playlists = playlistsByDest[destId];
        initialSelections[destId] = playlists.length === 1 ? playlists[0] : playlists;
      });

      return initialSelections;
    }
    return {};
  });
  const [showPlaylistSection, setShowPlaylistSection] = useState(null); // Store which destination is showing playlist
  const [showAuthorization, setShowAuthorization] = useState(false);
  const [currentAuthURL, setCurrentAuthURL] = useState(null);
  const [showAuthAlert, setShowAuthAlert] = useState(false);
  const [currentDestId, setCurrentDestId] = useState(null);
  const [selectedConfig, setSelectedConfig] = useState("blank");
  const [showSaveConfigDialog, setShowSaveConfigDialog] = useState(false);
  const [authorizedChannels, setAuthorizedChannels] = useState([]);
  const [isLoadingChannels, setIsLoadingChannels] = useState(false);
  const [error, setError] = useState(null);
  const [authorizedProfiles, setAuthorizedProfiles] = useState([]);
  const [isLoadingProfiles, setIsLoadingProfiles] = useState(false);
  const [profileSelections, setProfileSelections] = useState(() => {
    if (initialDestinations && initialDestinations.length > 0) {
      const initialSelections = {};
      initialDestinations.forEach((dest) => {
        if (dest.platform_userid) {
          if (!initialSelections[dest.id]) {
            initialSelections[dest.id] = [];
          }
          if (!initialSelections[dest.id].includes(dest.platform_userid)) {
            initialSelections[dest.id].push(dest.platform_userid);
          }
        }
      });
      return initialSelections;
    }
    return {};
  });
  const [excludeSelections, setExcludeSelections] = useState({});
  const [allDestinationsSelected, setAllDestinationsSelected] = useState(() => {
    // Only select "All Authorized Destinations" if it was explicitly selected before
    return initialDestinations && initialDestinations.some(dest => dest.id === "all_authorized");
  });

  // State for labeled destinations
  const [labeledDestinations, setLabeledDestinations] = useState([]);
  const [selectedLabels, setSelectedLabels] = useState(() => {
    return initialSelectedLabels || [];
  });
  const [isLoadingLabels, setIsLoadingLabels] = useState(false);
  const [labeledDestinationsSelected, setLabeledDestinationsSelected] = useState(() => {
    return initialSelectedLabels && initialSelectedLabels.length > 0;
  });
  const [showLabeledDestinationsSection, setShowLabeledDestinationsSection] = useState(false);

  // Track if user has manually interacted with "All Authorized Destinations"
  const [hasUserInteracted, setHasUserInteracted] = useState(false);

  // State for long video warning dialog
  const [showLongVideoWarning, setShowLongVideoWarning] = useState(false);
  const [ineligibleChannel, setIneligibleChannel] = useState(null);
  const [newConfigName, setNewConfigName] = useState(""); // Added missing state

  // Log initial state for debugging
  useEffect(() => {
    console.log("DestinationSelector initializing with:", {
      initialDestinations,
      selectedDestinations,
      playlistSelections,
    });
  }, []);

  // Fetch destinations from the backend
  const fetchDestinations = async () => {
    if (!user?.email) return;

    setIsLoading(true);
    setError(null);
    try {
      const response = await axios.get(
        `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/get_destinations/${user.email}`
      );

      if (response.status === 200 && response.data) {
        console.log("Fetched destinations:", response.data);
        setDestinations(response.data);
      }
    } catch (error) {
      console.error("Error fetching destinations:", error);
      if (error.response?.data?.error) {
        setError(error.response.data.error);
      } else if (error.response?.status) {
        setError(`Server error: ${error.response.status}`);
      } else {
        setError("Failed to load destinations. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch labeled destinations from the backend
  const fetchLabeledDestinations = async () => {
    if (!user?.email) return;

    setIsLoadingLabels(true);
    try {
      const response = await axios.get(
        `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/get_labeled_destinations`,
        {
          params: { sanatana_email: user.email }
        }
      );

      if (response.status === 200 && response.data) {
        console.log("Fetched labeled destinations:", response.data);
        setLabeledDestinations(response.data);
      }
    } catch (error) {
      console.error("Error fetching labeled destinations:", error);
    } finally {
      setIsLoadingLabels(false);
    }
  };

  useEffect(() => {
    fetchDestinations();
    fetchLabeledDestinations();
  }, [user]);

  // Fetch authorized profiles for TikTok, Facebook, Instagram
  const fetchAuthorizedProfiles = async () => {
    if (!user?.email) return;

    setIsLoadingProfiles(true);
    try {
      const response = await axios.get(
        `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/authorized_profiles/${user.email}`
      );

      if (response.status === 200 && response.data) {
        setAuthorizedProfiles(response.data);
        console.log('Fetched authorized profiles:', response.data);
      }
    } catch (error) {
      console.error("Error fetching authorized profiles:", error);
    } finally {
      setIsLoadingProfiles(false);
    }
  };

  useEffect(() => {
    fetchAuthorizedProfiles();
  }, [user]);

  // Initialize selections when destinations are loaded and "All Authorized Destinations" was selected
  useEffect(() => {
    if (allDestinationsSelected && destinations.length > 0) {
      const authorizedDestIds = destinations.filter(dest => dest.authorized && dest.enabled).map(dest => dest.id);
      setSelectedDestinations(prev => {
        const hasAllAuth = prev.includes("all_authorized");
        if (hasAllAuth && authorizedDestIds.length > 0) {
          // Merge with existing selections, avoiding duplicates
          const combined = [...new Set([...prev, ...authorizedDestIds])];
          return combined;
        }
        return prev;
      });
    }
  }, [destinations, allDestinationsSelected]);

  // Initialize exclude selections from initial destinations
  useEffect(() => {
    if (initialDestinations && initialDestinations.length > 0 && destinations.length > 0 && authorizedProfiles.length > 0 && authorizedChannels.length > 0) {
      const hasAllAuth = initialDestinations.some(dest => dest.id === "all_authorized");
      if (hasAllAuth) {
        const initialExcludes = {};

        // Get all possible authorized destinations
        const allPossibleDests = destinations.filter(dest => dest.authorized && dest.enabled && dest.id !== "all_authorized");

        // For each possible destination, check if it's missing from initial destinations
        allPossibleDests.forEach(dest => {
          const destId = dest.id;

          if (destId === "tiktok_video" || destId === "facebook_post" || destId === "instagram_post") {
            // For social media, check individual profiles
            const platform = destId.split('_')[0];
            const platformProfiles = authorizedProfiles.filter(profile =>
              profile.platform?.toLowerCase() === platform.toLowerCase() && profile.active === 1
            );

            platformProfiles.forEach(profile => {
              const profileKey = `${destId}_${profile.platform_userid}`;
              const profileInInitial = initialDestinations.some(d =>
                d.id === destId && d.platform_userid === profile.platform_userid
              );
              initialExcludes[profileKey] = !profileInInitial;
            });
          } else if (destId === "youtube_video" || destId === "youtube_shorts") {
            // For YouTube, check individual channels
            const youtubeChannels = authorizedChannels.filter(ch => ch.channel_name);

            youtubeChannels.forEach(channel => {
              const channelKey = `${destId}_${channel.channel_name}`;
              const channelInInitial = initialDestinations.some(d =>
                d.id === destId && d.playlist?.channel_name === channel.channel_name
              );
              initialExcludes[channelKey] = !channelInInitial;
            });
          } else {
            // For other destinations, use destination-level exclusion
            const destInInitial = initialDestinations.some(d => d.id === destId);
            initialExcludes[destId] = !destInInitial;
          }
        });

        console.log("Setting initial exclude selections:", initialExcludes);
        setExcludeSelections(initialExcludes);

        // Also ensure allDestinationsSelected is set to true, but only if user hasn't manually interacted
        if (!hasUserInteracted) {
          setAllDestinationsSelected(true);
        }
      }
    }
  }, [initialDestinations, destinations, authorizedProfiles, authorizedChannels, hasUserInteracted]);



  // Fetch authorized channels when component mounts
  useEffect(() => {
    const fetchAuthorizedChannels = async () => {
      if (!user?.email) return;

      setIsLoadingChannels(true);
      try {
        const response = await axios.get(
          `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/youtube/authorized_channels/${user.email}`
        );

        if (response.status === 200 && response.data) {
          const channels = response.data;
          setAuthorizedChannels(channels);
          console.log('Fetched authorized channels:', channels);

          // After fetching channels, restore saved selections or set defaults
          if (channels.length > 0) {
            // Skip initialization if we already have playlist selections from initial destinations
            if (Object.keys(playlistSelections).length > 0) {
              console.log("Playlist selections already initialized from initial destinations:", playlistSelections);
              return;
            }

            const savedSelections = loadYoutubeSelections();
            console.log('Loaded saved selections:', savedSelections);

            // Process each YouTube destination
            const youtubeDestinations = ["youtube_video", "youtube_shorts"];
            const newSelections = { ...playlistSelections };
            let selectionChanged = false;

            youtubeDestinations.forEach((destId) => {
              // Skip if not in selected destinations
              if (!selectedDestinations.includes(destId)) {
                console.log(`Skipping ${destId} - not in selected destinations`);
                return;
              }

              console.log(`Processing ${destId} for default selection`);

              // If we already have a selection for this destination, check if it's valid
              if (newSelections[destId]) {
                console.log(`Existing selection found for ${destId}:`, newSelections[destId]);
                const channelName = newSelections[destId].channel_name;
                const matchingChannel = findMatchingChannel(
                  channels,
                  channelName
                );

                // If the channel doesn't exist anymore, we need to update the selection
                if (!matchingChannel) {
                  console.log(`Channel ${channelName} not found, creating default selection`);
                  const defaultSelection = createDefaultSelection(
                    destId,
                    channels
                  );
                  if (defaultSelection) {
                    newSelections[destId] = defaultSelection;
                    selectionChanged = true;
                  }
                }
              }
              // If we don't have a selection yet, try to restore from saved selections
              else if (savedSelections && savedSelections[destId]) {
                console.log(`No current selection, but found saved selection for ${destId}`);
                const savedSelection = savedSelections[destId];
                const channelName = savedSelection.channel_name;
                const matchingChannel = findMatchingChannel(
                  channels,
                  channelName
                );

                // If the saved channel exists, use the saved selection
                if (matchingChannel) {
                  console.log(`Matching channel found for saved selection: ${channelName}`);
                  // For existing playlist type, verify the playlist still exists
                  if (savedSelection.type === "existing") {
                    // Extract playlist ID if it's a URL
                    const savedPlaylistId = savedSelection.id.includes(
                      "youtube.com"
                    )
                      ? extractPlaylistId(savedSelection.id)
                      : savedSelection.id;

                    const playlistExists = matchingChannel.playlists.some(
                      (p) => {
                        // Get the playlist ID from either id or url
                        const playlistId =
                          p.id || (p.url ? extractPlaylistId(p.url) : null);
                        return playlistId === savedPlaylistId;
                      }
                    );

                    if (playlistExists) {
                      console.log(`Playlist ${savedPlaylistId} exists, using saved selection`);
                      newSelections[destId] = savedSelection;
                      selectionChanged = true;
                    } else {
                      // Playlist doesn't exist anymore, use default
                      console.log(`Playlist ${savedPlaylistId} not found, creating default selection`);
                      newSelections[destId] = createDefaultSelection(
                        destId,
                        channels
                      );
                      selectionChanged = true;
                    }
                  } else {
                    // For other selection types (none, auto, new), just use the saved selection
                    console.log(`Using saved selection of type ${savedSelection.type}`);
                    newSelections[destId] = savedSelection;
                    selectionChanged = true;
                  }
                } else {
                  // Channel doesn't exist anymore, use default
                  console.log(`Channel ${channelName} not found, creating default selection`);
                  newSelections[destId] = createDefaultSelection(
                    destId,
                    channels
                  );
                  selectionChanged = true;
                }
              }
              // If no saved selection, create default
              else {
                console.log(`No selection found for ${destId}, creating default selection`);
                const defaultSelection = createDefaultSelection(
                  destId,
                  channels
                );
                if (defaultSelection) {
                  console.log(`Created default selection for ${destId}:`, defaultSelection);
                  newSelections[destId] = defaultSelection;
                  selectionChanged = true;
                }
              }
            });

            // Update state if any selections changed
            if (selectionChanged) {
              console.log('Updating playlist selections with:', newSelections);
              setPlaylistSelections(newSelections);
              saveYoutubeSelections(newSelections);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching authorized channels:", error);
      } finally {
        setIsLoadingChannels(false);
      }
    };

    fetchAuthorizedChannels();
  }, [user, selectedDestinations]);

  const handlePlaylistSelect = (destId, selection) => {
    setPlaylistSelections((prevSelections) => {
      const currentSelection = prevSelections[destId];
      let newSelection;

      // Handle multiple selections for YouTube
      if (destId === "youtube_video" || destId === "youtube_shorts") {
        if (Array.isArray(currentSelection)) {
          // Remove any existing selection for this channel first
          const filteredSelections = currentSelection.filter(sel => sel.channel_name !== selection.channel_name);

          // Check if the new selection is the same as what was removed
          const existingSelection = currentSelection.find(sel =>
            sel.channel_name === selection.channel_name && sel.type === selection.type &&
            (sel.type !== "existing" || sel.id === selection.id)
          );

          if (existingSelection) {
            // Remove the selection (toggle off)
            newSelection = filteredSelections;
          } else {
            // Add the new selection
            newSelection = [...filteredSelections, selection];
          }
        } else if (currentSelection) {
          // Convert single selection to array, remove if same channel, add if different
          if (currentSelection.channel_name === selection.channel_name &&
              currentSelection.type === selection.type &&
              (currentSelection.type !== "existing" || currentSelection.id === selection.id)) {
            newSelection = []; // Remove if same selection
          } else if (currentSelection.channel_name === selection.channel_name) {
            // Replace selection for same channel
            newSelection = [selection];
          } else {
            // Add selection for different channel
            newSelection = [currentSelection, selection];
          }
        } else {
          // First selection
          newSelection = selection;
        }
      } else {
        newSelection = selection;
      }

      const newSelections = {
        ...prevSelections,
        [destId]: newSelection,
      };

      // Save selections to localStorage
      saveYoutubeSelections(newSelections);

      return newSelections;
    });
    // Don't close the playlist section to allow multiple selections
    // setShowPlaylistSection(null);
  };

  const handleAuthorization = async (destId) => {
    // Store the current destination ID
    setCurrentDestId(destId);

    // Show the authorization alert modal
    setShowAuthAlert(true);
  };

  // Function to handle proceeding to SetupMedia
  // This is now just a placeholder since navigation is handled directly in the AuthorizationAlertModal
  const handleProceedToSetup = () => {
    // Close the alert (though this is now handled in the modal)
    setShowAuthAlert(false);

    // Log that we're proceeding
    console.log("Proceeding to setup from DestinationSelector");
  };

  const handleCloseAuthorization = async () => {
    setShowAuthorization(false);

    // Dummy API call
    try {
      const response = await fetch("https://api.example.com/authorize", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ destination: currentAuthURL }),
      });

      if (response.ok) {
        // Update authorization status (you'll need to implement this)
        // For now, just log success
        console.log("Authorization successful");
      }
    } catch (error) {
      console.error("Authorization error:", error);
    }
  };

  // Handle "Labeled Destinations" main selection
  const handleLabeledDestinationsToggle = () => {
    const newSelected = !labeledDestinationsSelected;
    setLabeledDestinationsSelected(newSelected);

    if (newSelected) {
      // When selecting labeled destinations, clear other selections
      setAllDestinationsSelected(false);
      setSelectedDestinations([]);
      setShowPlaylistSection(null);
      setExcludeSelections({});
      setHasUserInteracted(false);
      // Expand the section when selecting
      setShowLabeledDestinationsSection(true);
    } else {
      // When deselecting labeled destinations, clear label selections and collapse
      setSelectedLabels([]);
      setShowLabeledDestinationsSection(false);
    }
  };

  // Handle expand/collapse of labeled destinations section
  const handleLabeledDestinationsExpand = () => {
    setShowLabeledDestinationsSection(!showLabeledDestinationsSection);
  };

  // Handle individual label selection within the expanded section
  const handleLabelSelection = (labelId) => {
    const newSelectedLabels = selectedLabels.includes(labelId)
      ? selectedLabels.filter(id => id !== labelId)
      : [...selectedLabels, labelId];

    setSelectedLabels(newSelectedLabels);
  };

  // Create union of destinations from selected labels
  const getUnionDestinationsFromLabels = () => {
    if (selectedLabels.length === 0) return [];

    const unionDestinations = [];
    const seenDestinations = new Set();

    selectedLabels.forEach(labelId => {
      const labelData = labeledDestinations.find(ld => ld.id === labelId);
      if (labelData && labelData.destination_json) {
        labelData.destination_json.forEach(dest => {
          let uniqueKey;

          if (dest.id === "youtube_video" || dest.id === "youtube_shorts") {
            // For YouTube: uniqueness based on channel_id and playlist
            const channelId = dest.playlist?.channel_id || 'no_channel';
            const playlistInfo = dest.playlist?.id || dest.playlist?.type || 'no_playlist';
            uniqueKey = `${dest.id}_${channelId}_${playlistInfo}`;
          } else if (dest.id === "tiktok_video" || dest.id === "facebook_post" || dest.id === "instagram_post") {
            // For social media: uniqueness based on platform_userid and destination name
            const platformUserId = dest.platform_userid || 'no_user';
            uniqueKey = `${dest.id}_${platformUserId}`;
          } else {
            // For other destinations: uniqueness based on destination id
            uniqueKey = dest.id;
          }

          if (!seenDestinations.has(uniqueKey)) {
            seenDestinations.add(uniqueKey);
            unionDestinations.push(dest);
          }
        });
      }
    });

    return unionDestinations;
  };

  const handleToggleDestination = (destId) => {
    // Handle "All Authorized Destinations" selection
    if (destId === "all_authorized") {
      const newAllSelected = !allDestinationsSelected;
      setAllDestinationsSelected(newAllSelected);
      setHasUserInteracted(true); // Mark that user has manually interacted

      if (newAllSelected) {
        // Clear labeled destinations selection
        setSelectedLabels([]);
        setLabeledDestinationsSelected(false);
        setShowLabeledDestinationsSection(false);

        // Select all authorized destinations, replacing any previous selections
        const authorizedDestIds = destinations.filter(dest => dest.authorized && dest.enabled).map(dest => dest.id);
        setSelectedDestinations(["all_authorized", ...authorizedDestIds]);

        // Close any open playlist sections
        setShowPlaylistSection(null);

        // Initialize exclude selections for all authorized destinations
        // Only reset if we don't already have exclude selections (from initial destinations)
        if (Object.keys(excludeSelections).length === 0) {
          const newExcludeSelections = {};
          authorizedDestIds.forEach(id => {
            newExcludeSelections[id] = false;
          });
          setExcludeSelections(newExcludeSelections);
        }
      } else {
        // Deselect all destinations
        setSelectedDestinations([]);
        setExcludeSelections({});
      }
      return;
    }

    const dest = destinations.find((d) => d.id === destId);

    // Check authorization first for TikTok, Facebook, Instagram
    if (dest && !dest.authorized && (destId === "tiktok_video" || destId === "facebook_post" || destId === "instagram_post")) {
      handleAuthorization(destId);
      return;
    }

    // Check authorization for YouTube (existing logic)
    if (dest && !dest.authorized) {
      handleAuthorization(destId);
      return;
    }

    // Don't allow individual selection if labels are selected
    if (selectedLabels.length > 0) {
      return;
    }

    // Clear labeled destinations when selecting individual destinations
    if (!selectedDestinations.includes(destId)) {
      // Only clear when adding a new destination, not when removing
      setSelectedLabels([]);
      setLabeledDestinationsSelected(false);
      setShowLabeledDestinationsSection(false);
      setAllDestinationsSelected(false);
      setHasUserInteracted(false);
    }

    setSelectedDestinations((prev) => {
      const newDestinations = prev.includes(destId)
        ? prev.filter((id) => id !== destId)
        : [...prev, destId];

      // If adding a YouTube destination and we have authorized channels,
      // set default selection if none exists
      if (
        !prev.includes(destId) &&
        (destId === "youtube_shorts" || destId === "youtube_video") &&
        authorizedChannels.length > 0
      ) {
        // Use setTimeout to ensure this runs after state update
        setTimeout(() => {
          setPlaylistSelections((prevSelections) => {
            console.log("Setting default selection for", destId);
            console.log("Current selections:", prevSelections);
            console.log("Authorized channels:", authorizedChannels);

            // Only create default selection if no existing selection and no initial destinations
            if (!prevSelections[destId] && (!initialDestinations || initialDestinations.length === 0)) {
              const defaultSelection = createDefaultSelection(
                destId,
                authorizedChannels
              );

              if (defaultSelection) {
                console.log("Created default selection:", defaultSelection);
                const newSelections = {
                  ...prevSelections,
                  [destId]: defaultSelection,
                };

                // Save to localStorage
                saveYoutubeSelections(newSelections);

                return newSelections;
              }
            }
            return prevSelections;
          });
        }, 0);
      }

      // If adding a TikTok, Facebook, or Instagram destination and we have authorized profiles,
      // set default profile selection if none exists
      if (
        !prev.includes(destId) &&
        (destId === "tiktok_video" || destId === "facebook_post" || destId === "instagram_post") &&
        authorizedProfiles.length > 0
      ) {
        setTimeout(() => {
          setProfileSelections((prevSelections) => {
            if (!prevSelections[destId]) {
              const platform = destId.split('_')[0];
              const platformProfiles = authorizedProfiles.filter(profile =>
                profile.platform?.toLowerCase() === platform.toLowerCase() && profile.active === 1
              );

              if (platformProfiles.length > 0) {
                const newSelections = {
                  ...prevSelections,
                  [destId]: [platformProfiles[0].platform_userid]
                };
                return newSelections;
              }
            }
            return prevSelections;
          });
        }, 0);
      }

      return newDestinations;
    });
  };

  const getPlaylistDisplay = (destId) => {
    const selection = playlistSelections[destId];
    if (!selection) return null;

    // Handle array of selections (multiple channels)
    if (Array.isArray(selection)) {
      if (selection.length === 0) return null;
      if (selection.length === 1) {
        // Single selection in array, treat as single
        const sel = selection[0];
        if (sel.type === "none") {
          return `${sel.channel_name} - No playlist`;
        } else if (sel.type === "auto") {
          return `${sel.channel_name} - Auto playlist`;
        } else if (sel.type === "new") {
          return `${sel.channel_name} - New: ${sel.name}`;
        } else {
          return `${sel.channel_name} - ${sel.name}`;
        }
      } else {
        // Multiple selections - show all channel names with their playlist details
        return selection.map(sel => {
          if (sel.type === "none") {
            return `${sel.channel_name} - No playlist`;
          } else if (sel.type === "auto") {
            return `${sel.channel_name} - Auto playlist`;
          } else if (sel.type === "new") {
            return `${sel.channel_name} - New: ${sel.name}`;
          } else {
            return `${sel.channel_name} - ${sel.name}`;
          }
        }).join(", ");
      }
    }

    // Handle single selection
    if (selection.type === "none") {
      return `${selection.channel_name} - No playlist`;
    } else if (selection.type === "auto") {
      return `${selection.channel_name} - Auto playlist`;
    } else if (selection.type === "new") {
      return `${selection.channel_name} - New: ${selection.name}`;
    } else {
      return `${selection.channel_name} - ${selection.name}`;
    }
  };

  const getProfileDisplay = (destId) => {
    const selection = profileSelections[destId];
    if (!selection || selection.length === 0) return "No Profile Selected";

    return selection.join(", ");
  };

  const handleProfileSelect = (destId, selectedProfiles) => {
    setProfileSelections(prev => ({
      ...prev,
      [destId]: selectedProfiles
    }));
  };

  const handleExcludeToggle = (destId, profileId = null) => {
    if (profileId) {
      // Handle individual profile/channel exclusion
      const profileKey = `${destId}_${profileId}`;
      setExcludeSelections(prev => ({
        ...prev,
        [profileKey]: !prev[profileKey]
      }));
    } else {
      // Handle destination-level exclusion
      const isSocialMedia = destId === "tiktok_video" || destId === "facebook_post" || destId === "instagram_post";

      if (isSocialMedia) {
        // For social media, toggle all profiles for this destination
        const platform = destId.split('_')[0];
        const platformProfiles = authorizedProfiles.filter(profile =>
          profile.platform?.toLowerCase() === platform.toLowerCase() && profile.active === 1
        );

        // Check if any profile is currently excluded
        const anyExcluded = platformProfiles.some(profile => {
          const profileKey = `${destId}_${profile.platform_userid}`;
          return excludeSelections[profileKey];
        });

        // Toggle all profiles to the opposite state
        const newExclusions = {};
        platformProfiles.forEach(profile => {
          const profileKey = `${destId}_${profile.platform_userid}`;
          newExclusions[profileKey] = !anyExcluded;
        });

        setExcludeSelections(prev => ({
          ...prev,
          ...newExclusions
        }));
      } else if (destId === "youtube_video" || destId === "youtube_shorts") {
        // For YouTube, toggle all channels for this destination
        const youtubeChannels = authorizedChannels.filter(ch => ch.channel_name);

        // Check if any channel is currently excluded
        const anyExcluded = youtubeChannels.some(channel => {
          const channelKey = `${destId}_${channel.channel_name}`;
          return excludeSelections[channelKey];
        });

        // Toggle all channels to the opposite state
        const newExclusions = {};
        youtubeChannels.forEach(channel => {
          const channelKey = `${destId}_${channel.channel_name}`;
          newExclusions[channelKey] = !anyExcluded;
        });

        setExcludeSelections(prev => ({
          ...prev,
          ...newExclusions
        }));
      } else {
        // For non-social media destinations, use destination-level exclusion
        setExcludeSelections(prev => ({
          ...prev,
          [destId]: !prev[destId]
        }));
      }
    }
  };

  const processPlaylistSelection = (selection) => {
    if (!selection) return null;

    // Find the channel to get its ID and other details
    const channel = selection.channel_name
      ? authorizedChannels.find(
        (c) => c.channel_name === selection.channel_name
      )
      : null;

    // Always include channel_name and channel_id if they exist
    const baseSelection = {};

    if (selection.channel_name) {
      baseSelection.channel_name = selection.channel_name;
    }

    if (channel && channel.channel_id) {
      baseSelection.channel_id = channel.channel_id;
    }

    if (selection.type === "auto") {
      return { ...baseSelection, type: "auto" };
    } else if (selection.type === "none") {
      return { ...baseSelection, type: "none" };
    } else if (selection.type === "new") {
      return { ...baseSelection, type: "new", name: selection.name };
    } else if (selection.type === "existing") {
      let playlistName = selection.name;

      // If we have the channel, try to find the playlist to get its name
      if (channel && channel.playlists) {
        const playlist = channel.playlists.find((p) => {
          const playlistId = p.id || (p.url ? extractPlaylistId(p.url) : null);
          return playlistId === selection.id;
        });

        if (playlist) {
          playlistName = playlist.name;
        }
      }

      return {
        ...baseSelection,
        type: "existing",
        id: selection.id,
        name: playlistName || selection.name || `Playlist ${selection.id}`,
      };
    }
    return null;
  };

  const handleConfigChange = (configId) => {
    setSelectedConfig(configId);
    if (configId !== "blank") {
      // Dummy method to initialize destinations
      console.log("Initializing destinations for config:", configId);
      // Here you would normally load the configuration and set the state
    }
  };

  const checkForConfigChanges = () => {
    // Dummy method to check for changes
    console.log("Checking for configuration changes...");
    return true; // For now, always return true to show the dialog
  };

  const handleCreateNewConfig = () => {
    if (newConfigName.trim()) {
      // Dummy method to create new config
      console.log("Creating new configuration:", newConfigName);
      // Here you would normally save the configuration
      setShowSaveConfigDialog(false);
      setNewConfigName("");
      handleContinue();
    }
  };

  const handleContinue = () => {
    if (selectedConfig !== "blank" && checkForConfigChanges()) {
      setShowSaveConfigDialog(true);
      return;
    }

    // Check if the video is longer than 15 minutes
    const isVideoLongerThan15Min = wizardData?.duration > 900;

    // If the video is longer than 15 minutes, check if all selected YouTube channels are eligible
    if (isVideoLongerThan15Min) {
      for (const destId of selectedDestinations) {
        // Only check YouTube destinations
        if (!destId.startsWith('youtube')) continue;

        const selection = playlistSelections[destId];
        if (!selection || !selection.channel_name) continue;

        // Find the channel in authorizedChannels
        const channel = authorizedChannels.find(ch => ch.channel_name === selection.channel_name);

        // If channel is not eligible for long uploads, show warning
        if (channel && channel.longUploadsStatus !== 'eligible') {
          setIneligibleChannel(channel);
          setShowLongVideoWarning(true);
          return;
        }
      }
    }

    // Proceed with normal continue logic
    let destinations = [];

    if (selectedLabels.length > 0) {
      // Handle labeled destinations selection
      destinations = getUnionDestinationsFromLabels();
    } else if (allDestinationsSelected) {
      // Handle "All Authorized Destinations" selection - only include authorized destinations, not individual selections
      const authorizedDests = allDestinations.filter(dest => dest.authorized && dest.enabled && dest.id !== "all_authorized");

      // Add "all_authorized" marker to destinations
      destinations.push({
        id: "all_authorized",
        name: "All Authorized Destinations",
        ...(wizardData?.categories && { categories: [...wizardData.categories] })
      });

      for (const dest of authorizedDests) {
        const destId = dest.id;

        // Skip if excluded (only for non-social media destinations)
        if (excludeSelections[destId] && destId !== "tiktok_video" && destId !== "facebook_post" && destId !== "instagram_post") continue;

        if (destId === "youtube_video" || destId === "youtube_shorts") {
          // Handle YouTube destinations - use actual playlist selections
          const selection = playlistSelections[destId];
          if (selection && Array.isArray(selection)) {
            // Multiple channel selections
            selection.forEach(sel => {
              // Check if this specific channel is excluded
              const channelKey = `${destId}_${sel.channel_name}`;
              if (excludeSelections[channelKey]) return;

              destinations.push({
                id: destId,
                name: dest.title,
                playlist: processPlaylistSelection(sel),
                ...(wizardData?.categories && { categories: [...wizardData.categories] })
              });
            });
          } else if (selection) {
            // Single channel selection
            const channelKey = `${destId}_${selection.channel_name}`;
            if (!excludeSelections[channelKey]) {
              destinations.push({
                id: destId,
                name: dest.title,
                playlist: processPlaylistSelection(selection),
                ...(wizardData?.categories && { categories: [...wizardData.categories] })
              });
            }
          } else {
            // No playlist selections made - fall back to all authorized channels with no playlist
            const authorizedYouTubeChannels = authorizedChannels.filter(ch => ch.channel_name);
            authorizedYouTubeChannels.forEach(channel => {
              // Check if this specific channel is excluded
              const channelKey = `${destId}_${channel.channel_name}`;
              if (excludeSelections[channelKey]) return;

              destinations.push({
                id: destId,
                name: dest.title,
                playlist: {
                  channel_name: channel.channel_name,
                  type: "none"
                },
                ...(wizardData?.categories && { categories: [...wizardData.categories] })
              });
            });
          }
        } else if (destId === "tiktok_video" || destId === "facebook_post" || destId === "instagram_post") {
          // Handle social media destinations with profiles - get all authorized profiles
          const platform = destId.split('_')[0];
          const platformProfiles = authorizedProfiles.filter(profile =>
            profile.platform?.toLowerCase() === platform.toLowerCase() && profile.active === 1
          );
          platformProfiles.forEach(profile => {
            // Check if this specific profile is excluded
            const profileKey = `${destId}_${profile.platform_userid}`;
            if (excludeSelections[profileKey]) return;

            destinations.push({
              id: destId,
              name: dest.title,
              platform_userid: profile.platform_userid,
              ...(wizardData?.categories && { categories: [...wizardData.categories] })
            });
          });
        }
      }
    } else {
      // Handle individual destination selections
      destinations = selectedDestinations.map((destId) => {
        const dest = allDestinations.find((d) => d.id === destId);

        if (destId === "youtube_video" || destId === "youtube_shorts") {
          // Handle multiple YouTube channels
          const selection = playlistSelections[destId];
          if (selection && Array.isArray(selection)) {
            return selection.map(sel => ({
              id: destId,
              name: dest ? dest.title : destId,
              playlist: processPlaylistSelection(sel),
              ...(wizardData?.categories && { categories: [...wizardData.categories] })
            }));
          } else {
            return {
              id: destId,
              name: dest ? dest.title : destId,
              playlist: processPlaylistSelection(selection),
              ...(wizardData?.categories && { categories: [...wizardData.categories] })
            };
          }
        } else if (destId === "tiktok_video" || destId === "facebook_post" || destId === "instagram_post") {
          const profiles = profileSelections[destId] || [];
          return profiles.filter(profileId => {
            // Check if this profile is excluded when in "All Authorized" mode
            const profileKey = `${destId}_${profileId}`;
            return !excludeSelections[profileKey];
          }).map(profileId => ({
            id: destId,
            name: dest ? dest.title : destId,
            platform_userid: profileId,
            ...(wizardData?.categories && { categories: [...wizardData.categories] })
          }));
        } else {
          return {
            id: destId,
            name: dest ? dest.title : destId,
            ...(wizardData?.categories && { categories: [...wizardData.categories] })
          };
        }
      }).flat();
    }

    // Log the final destinations array for debugging
    console.log("Final destinations with categories:", destinations);

    // Include label information for restoration when coming back
    const result = { destinations };
    if (selectedLabels.length > 0) {
      result.selectedLabels = selectedLabels;
    }

    onComplete(result);
  };

  // Filter destinations by enabled status
  const enabledDestinations = destinations.filter((dest) => dest.enabled);
  const disabledDestinations = destinations.filter((dest) => !dest.enabled);

  // Add "All Authorized Destinations" at the top if any destination is authorized
  const hasAuthorizedDestinations = enabledDestinations.some(dest => dest.authorized);
  const allDestinations = [];

  if (hasAuthorizedDestinations) {
    allDestinations.push({
      id: "all_authorized",
      title: "All Authorized Destinations",
      friendly_name: "All Platforms",
      icon: "globe",
      type: "font-awesome",
      enabled: true,
      authorized: true
    });
  }

  allDestinations.push(...enabledDestinations, ...disabledDestinations);

  // Check if any YouTube destinations are selected
  // const hasSelectedYouTube = selectedDestinations.some((id) =>
  //   id.startsWith("youtube")
  // );

  // Check if a video is longer than 15 minutes (900 seconds)
  const isLongVideo = wizardData?.duration > 900;

  // Check if a channel is eligible for long uploads
  const isChannelEligibleForLongUploads = (channelName) => {
    // Find the channel in the authorizedChannels array
    const channel = authorizedChannels.find(ch => ch.channel_name === channelName);
    return channel?.longUploadsStatus === "eligible";
  };

  // Check if a destination has a valid playlist selection
  // const isValidSelection = (destId) => {
  //   const selection = playlistSelections[destId];
  //   return (
  //     selection?.type === "none" ||
  //     selection?.type === "auto" ||
  //     selection?.type === "new" ||
  //     selection?.id
  //   ); // for existing playlist
  // };

  const renderDestination = (dest) => {
    const isAllAuthorized = dest.id === "all_authorized";
    // For "All Authorized Destinations", don't show as selected if labeled destinations are selected
    const isSelected = isAllAuthorized
      ? (allDestinationsSelected && !labeledDestinationsSelected)
      : (selectedDestinations.includes(dest.id) || (allDestinationsSelected && !labeledDestinationsSelected));
    const isYouTube = dest.id.startsWith("youtube");
    const isSocialMedia = dest.id === "tiktok_video" || dest.id === "facebook_post" || dest.id === "instagram_post";
    const playlistSelection = playlistSelections[dest.id];
    const profileSelection = profileSelections[dest.id];

    // Check if destination is excluded
    let isExcluded = false;
    if (isSocialMedia) {
      // Check if any profile for this destination is excluded
      const platform = dest.id.split('_')[0];
      const platformProfiles = authorizedProfiles.filter(profile =>
        profile.platform?.toLowerCase() === platform.toLowerCase() && profile.active === 1
      );
      isExcluded = platformProfiles.some(profile => {
        const profileKey = `${dest.id}_${profile.platform_userid}`;
        return excludeSelections[profileKey];
      });
    } else if (isYouTube) {
      // Check if any channel for this YouTube destination is excluded
      const youtubeChannels = authorizedChannels.filter(ch => ch.channel_name);
      isExcluded = youtubeChannels.some(channel => {
        const channelKey = `${dest.id}_${channel.channel_name}`;
        return excludeSelections[channelKey];
      });
    } else {
      // For other destinations, use destination-level exclusion
      isExcluded = excludeSelections[dest.id];
    }

    const canAuthorize = dest.enabled;

    // Gray out destinations based on mutual exclusion
    // Gray out individual destinations when "All Authorized Destinations" is selected (but not when labeled destinations are selected)
    // Gray out "All Authorized Destinations" when labeled destinations are selected
    // Gray out individual destinations when labeled destinations are selected
    const isGrayedOut = (allDestinationsSelected && !labeledDestinationsSelected && !isAllAuthorized) ||
                        (labeledDestinationsSelected && isAllAuthorized) ||
                        (labeledDestinationsSelected && !isAllAuthorized);

    return (
      <React.Fragment key={dest.id}>
        <TouchableOpacity
          onPress={() => (dest.enabled || dest.id === "all_authorized") && handleToggleDestination(dest.id)}
          className={`flex-col md:flex-row p-4 mb-3 rounded-lg border ${
            !dest.enabled
              ? "border-gray-200 bg-gray-50 opacity-50"
              : isGrayedOut
              ? "border-gray-300 bg-gray-100 opacity-75"
              : isSelected
              ? isAllAuthorized
                ? "border-green-500 bg-green-50"
                : "border-blue-500 bg-blue-50"
              : "border-gray-200 bg-white"
            }`}
          disabled={!dest.enabled || (isGrayedOut && dest.id !== "all_authorized")}
          style={{ flexWrap: "wrap" }}
        >
          {/* Top row with icon and title - always visible */}
          <View className="flex-row items-center w-full">
            <Icon
              name={dest.icon}
              type={dest.type || "font-awesome"}
              size={24}
              color={
                dest.enabled ? (isSelected ? "#3B82F6" : "#4B5563") : "#9CA3AF"
              }
            />
            <View className="flex-1 ml-3">
              <Text
                className={`${dest.enabled
                    ? isSelected
                      ? isAllAuthorized
                        ? "text-green-600 font-medium"
                        : "text-blue-600 font-medium"
                      : "text-gray-700"
                    : "text-gray-400"
                  }`}
              >
                {dest.title}
              </Text>

              {/* Show authorization status for enabled destinations */}
              {dest.enabled && (
                <View className="flex-row items-center mt-1">
                  {dest.authorized ? (
                    <View className="flex-row items-center">
                      <Text className="text-xs text-green-600 mr-1">
                        Authorized
                      </Text>
                      <Icon
                        name="check-circle"
                        type="font-awesome"
                        size={12}
                        color="#16A34A"
                      />
                    </View>
                  ) : (
                    <Text className="text-xs text-orange-500">
                      Your {dest.friendly_name} is not Authorized. Select to Authorize.
                    </Text>
                  )}
                </View>
              )}

              {isSelected && isYouTube && (
                <Text className="text-sm text-gray-500 mt-1 flex-wrap">
                  {playlistSelection
                    ? getPlaylistDisplay(dest.id)
                    : "No Channel Selected"}
                </Text>
              )}

              {isSelected && isSocialMedia && (
                <Text className="text-sm text-gray-500 mt-1 flex-wrap">
                  {getProfileDisplay(dest.id)}
                </Text>
              )}

              {allDestinationsSelected && dest.authorized && !isAllAuthorized && (
                <View className="flex-row items-center mt-2">
                  <TouchableOpacity
                    onPress={() => handleExcludeToggle(dest.id)}
                    className="flex-row items-center"
                  >
                    <Icon
                      name={isExcluded ? "check-square" : "square"}
                      type="font-awesome"
                      size={16}
                      color={isExcluded ? "#3B82F6" : "#6B7280"}
                    />
                    <Text className="ml-2 text-sm text-gray-600">Exclude</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>

          {/* Second row with authorization and playlist selection - only visible when selected */}
          {isSelected && (
            <View className="flex-row justify-end items-center w-full mt-3 md:mt-0 md:ml-auto md:w-auto">
              {/* Authorization Button (only show if not authorized) */}
              {!dest.authorized && (
                <View className="mr-4">
                  <TouchableOpacity
                    onPress={() => handleAuthorization(dest.id)}
                    className="flex-row items-center"
                    disabled={!canAuthorize}
                  >
                    <Text
                      className={`text-sm ${canAuthorize ? "text-blue-600" : "text-gray-400"
                        }`}
                    >
                      Authorize Media
                    </Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* Playlist Selection Button */}
              {isYouTube && (
                <TouchableOpacity
                  onPress={() =>
                    setShowPlaylistSection(
                      showPlaylistSection === dest.id ? null : dest.id
                    )
                  }
                  className="flex-row items-center"
                  disabled={allDestinationsSelected && !isExcluded}
                >
                  <Text className={`text-sm mr-2 ${allDestinationsSelected && !isExcluded ? "text-gray-400" : "text-blue-600"}`}>
                    {playlistSelection ? "Change playlist" : "Select playlist"}
                  </Text>
                  <Icon
                    name={
                      showPlaylistSection === dest.id
                        ? "chevron-up"
                        : "chevron-down"
                    }
                    type="font-awesome"
                    size={12}
                    color={allDestinationsSelected && !isExcluded ? "#9CA3AF" : "#2563EB"}
                  />
                </TouchableOpacity>
              )}

              {/* Profile Selection Button */}
              {isSocialMedia && dest.authorized && (
                <TouchableOpacity
                  onPress={() =>
                    setShowPlaylistSection(
                      showPlaylistSection === dest.id ? null : dest.id
                    )
                  }
                  className="flex-row items-center"
                  disabled={allDestinationsSelected && !isExcluded}
                >
                  <Text className={`text-sm mr-2 ${allDestinationsSelected && !isExcluded ? "text-gray-400" : "text-blue-600"}`}>
                    {profileSelection && profileSelection.length > 0 ? "Change profiles" : "Select profiles"}
                  </Text>
                  <Icon
                    name={
                      showPlaylistSection === dest.id
                        ? "chevron-up"
                        : "chevron-down"
                    }
                    type="font-awesome"
                    size={12}
                    color={allDestinationsSelected && !isExcluded ? "#9CA3AF" : "#2563EB"}
                  />
                </TouchableOpacity>
              )}
            </View>
          )}

          {!dest.enabled && (
            <View className="w-full md:w-auto md:ml-auto">
              <Text className="text-xs text-gray-400 mt-2">Coming soon</Text>
            </View>
          )}
        </TouchableOpacity>

        {isYouTube && isSelected && showPlaylistSection === dest.id && (
          <YoutubeDestination
            dest={dest} // Pass the destination object
            playlistSelections_temp={playlistSelections} // Pass the state object
            onSelectPlaylist={handlePlaylistSelect} // Pass the handler function
            authorizedChannels={authorizedChannels} // Pass the authorized channels
            isLoadingChannels={isLoadingChannels} // Pass loading state
            excludeMode={allDestinationsSelected && dest.authorized && !isAllAuthorized}
            excludeSelections={excludeSelections}
            onExcludeToggle={(destId, channelName) => handleExcludeToggle(destId, channelName)}
          />
        )}

        {isSocialMedia && isSelected && showPlaylistSection === dest.id && !allDestinationsSelected && (
          <ProfileDestination
            dest={dest}
            profileSelections={profileSelections}
            onSelectProfile={handleProfileSelect}
            authorizedProfiles={authorizedProfiles}
            isLoadingProfiles={isLoadingProfiles}
          />
        )}

        {allDestinationsSelected && dest.authorized && !isAllAuthorized && isExcluded && showPlaylistSection === dest.id && (
          <ProfileDestination
            dest={dest}
            profileSelections={profileSelections}
            onSelectProfile={handleProfileSelect}
            authorizedProfiles={authorizedProfiles}
            isLoadingProfiles={isLoadingProfiles}
            excludeMode={true}
            onExcludeToggle={(destId, profileId) => handleExcludeToggle(destId, profileId)}
            isExcluded={isExcluded}
            excludeSelections={excludeSelections}
          />
        )}
      </React.Fragment>
    );
  };

  return (
    <View
      className="flex-1 flex-col"
      style={{ height: "100%", display: "flex", flexDirection: "column" }}
    >
      {/* Header with Configuration Picker */}
      <View className="px-5 pt-5">
        <View className="flex-row justify-between items-center mb-4">
          <Text className="text-2xl font-bold text-gray-800">
            Select Destinations
          </Text>
          <View className="my-4 w-full px-4">
            {/* <View className="items-end">
    <Text className="text-sm font-semibold text-gray-700 text-right mb-1 max-w-[160px]">
      Select Pre-Configured Destinations
    </Text>

    <View className="border border-gray-200 rounded-lg w-40 opacity-50">
      <Picker
        enabled={false}
        selectedValue={selectedConfig}
        onValueChange={handleConfigChange}
        style={{ height: 40 }}
      >
        <Picker.Item label="Blank Configuration" value="blank" />
        {DestinationConfigurations.map((config) => (
          <Picker.Item
            key={config.id}
            label={config.name}
            value={config.id}
          />
        ))}
      </Picker>
    </View>

    <Text className="text-xs text-gray-500 mt-1 text-right w-40">
      Coming soon
    </Text>
  </View> */}
          </View>
        </View>
        <Text className="text-gray-600 mb-6">
          Choose where you want to upload your content
        </Text>
      </View>

      <ScrollView
        className="flex-1 px-5"
        contentContainerStyle={{ paddingBottom: 95 }}
      >
        {error ? (
          <View className="flex-1 justify-center items-center py-10">
            <Icon name="exclamation-circle" type="font-awesome" size={24} color="#EF4444" />
            <Text className="text-red-600 text-base mt-2 mb-4 text-center">{error}</Text>
            <TouchableOpacity
              className="bg-red-500 px-4 py-2 rounded-lg"
              onPress={() => {
                setError(null);
                fetchDestinations();
              }}
            >
              <Text className="text-white font-medium">Retry</Text>
            </TouchableOpacity>
          </View>
        ) : isLoading ? (
          <View className="flex-1 justify-center items-center py-10">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="mt-4 text-gray-600">Loading destinations...</Text>
          </View>
        ) : allDestinations.length > 0 ? (
          <>
            {/* Labeled Destinations as a destination-style component */}
            {labeledDestinations.length > 0 && (
              <View
                className={`flex-col md:flex-row p-4 mb-3 rounded-lg border ${
                  labeledDestinationsSelected
                    ? "border-green-500 bg-green-50"
                    : "border-gray-200 bg-white"
                }`}
                style={{ flexWrap: "wrap" }}
              >
                {/* Top row with icon and title */}
                <View className="flex-row items-center w-full">
                  <Icon
                    name="tags"
                    type="font-awesome"
                    size={24}
                    color={labeledDestinationsSelected ? "#10B981" : "#6B7280"}
                  />
                  <TouchableOpacity
                    className="flex-1 ml-3"
                    onPress={handleLabeledDestinationsToggle}
                  >
                    <Text className={`text-lg font-semibold ${
                      labeledDestinationsSelected ? "text-green-800" : "text-gray-800"
                    }`}>
                      Labeled Destinations
                    </Text>
                    <Text className="text-sm text-gray-500">
                      {labeledDestinations.length} label{labeledDestinations.length !== 1 ? 's' : ''} available
                    </Text>
                  </TouchableOpacity>

                  {/* Selection indicator and expand button */}
                  <View className="flex-row items-center">
                    {selectedLabels.length > 0 && (
                      <Text className="text-sm text-green-600 mr-2">
                        {selectedLabels.length} selected
                      </Text>
                    )}
                    <TouchableOpacity onPress={handleLabeledDestinationsToggle}>
                      <Icon
                        name={labeledDestinationsSelected ? "check-circle" : "circle-o"}
                        type="font-awesome"
                        size={20}
                        color={labeledDestinationsSelected ? "#10B981" : "#9CA3AF"}
                        style={{ marginRight: 8 }}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={handleLabeledDestinationsExpand}>
                      <Icon
                        name={showLabeledDestinationsSection ? "chevron-up" : "chevron-down"}
                        type="font-awesome"
                        size={12}
                        color={labeledDestinationsSelected ? "#10B981" : "#6B7280"}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            )}

            {/* Expanded label selection section */}
            {showLabeledDestinationsSection && labeledDestinations.length > 0 && (
              <View className="mb-4 ml-4 mr-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <Text className="text-sm font-medium text-gray-700 mb-3">
                  Select Labels:
                </Text>
                {labeledDestinations.map((labelDest) => (
                  <TouchableOpacity
                    key={labelDest.id}
                    onPress={() => handleLabelSelection(labelDest.id)}
                    className={`flex-row items-center justify-between p-3 mb-2 rounded-lg ${
                      selectedLabels.includes(labelDest.id)
                        ? "bg-blue-100 border border-blue-300"
                        : "bg-white border border-gray-200"
                    }`}
                  >
                    <View className="flex-1">
                      <Text className={`font-medium ${
                        selectedLabels.includes(labelDest.id)
                          ? "text-blue-800"
                          : "text-gray-800"
                      }`}>
                        {labelDest.label}
                      </Text>
                      <Text className="text-sm text-gray-500">
                        {labelDest.destination_json?.length || 0} destination{labelDest.destination_json?.length !== 1 ? 's' : ''}
                      </Text>
                    </View>
                    <Icon
                      name={selectedLabels.includes(labelDest.id) ? "check-circle" : "circle-o"}
                      type="font-awesome"
                      size={20}
                      color={selectedLabels.includes(labelDest.id) ? "#3B82F6" : "#9CA3AF"}
                    />
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {allDestinations.map(renderDestination)}
          </>
        ) : (
          <View className="flex-1 justify-center items-center py-10">
            <Text className="text-gray-600">No destinations available</Text>
          </View>
        )}
      </ScrollView>

      <View
        className="bg-white"
        style={
          buttonContainerStyle || {
            position: "sticky",
            bottom: 0,
            left: 0,
            right: 0,
            zIndex: 10,
          }
        }
      >
        <TouchableOpacity
          onPress={handleContinue}
          disabled={selectedDestinations.length === 0 && selectedLabels.length === 0}
          className={`rounded-lg py-4 ${(selectedDestinations.length > 0 || selectedLabels.length > 0) ? "bg-blue-600" : "bg-gray-300"
            }`}
          style={{ marginHorizontal: 20 }}
        >
          <Text className="text-white text-center font-semibold">Continue</Text>
        </TouchableOpacity>
      </View>
      {showAuthorization && (
        <AuthorizationWebView
          url={currentAuthURL}
          onClose={handleCloseAuthorization}
          onAuthorized={() => { }}
        />
      )}

      {/* Authorization Alert Modal */}
      <AuthorizationAlertModal
        visible={showAuthAlert}
        onClose={() => setShowAuthAlert(false)}
        onProceed={handleProceedToSetup}
        destinationTitle={
          currentDestId
            ? allDestinations.find((d) => d.id === currentDestId)?.friendly_name || ""
            : ""
        }
        platformId={
          // Map destination IDs to their corresponding media platform IDs
          currentDestId === "youtube_video" 
            ? "youtube"
            : currentDestId === "tiktok_video"
            ? "tiktok"
            : currentDestId === "facebook_post"
            ? "facebook"
            : currentDestId === "instagram_post"
            ? "instagram"
            : currentDestId
        }
      />

      {/* Configuration Dialog */}
      {showSaveConfigDialog && (
        <View className="absolute inset-0 bg-black bg-opacity-50 justify-center items-center z-50">
          <View className="bg-white p-5 rounded-lg w-11/12">
            <Text className="text-lg font-bold mb-3">Save Configuration</Text>
            <TextInput
              value={newConfigName}
              onChangeText={setNewConfigName}
              placeholder="Enter configuration name"
              className="border border-gray-200 rounded-lg p-2 mb-3"
            />
            <View className="flex-row justify-end">
              <TouchableOpacity
                onPress={() => setShowSaveConfigDialog(false)}
                className="px-4 py-2 mr-2"
              >
                <Text className="text-gray-600">Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleCreateNewConfig}
                className="bg-blue-600 px-4 py-2 rounded-lg"
              >
                <Text className="text-white">Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* Long Video Warning Dialog */}
      {showLongVideoWarning && (
        <View className="absolute inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <View className="bg-white p-5 rounded-lg w-11/12 max-w-md">
            <Text className="text-lg font-bold mb-3">Channel Not Eligible for Long Videos</Text>

            <Text className="text-red-600 font-medium mb-2">
              Warning: This video is longer than 15 minutes
            </Text>

            <Text className="mb-4">
              The channel <Text className="font-bold">{ineligibleChannel?.channel_name}</Text> is not eligible to upload videos longer than 15 minutes.
            </Text>

            <Text className="mb-4">
              To upload longer videos, you need to verify this channel on YouTube.
            </Text>

            <View className="bg-gray-100 p-3 rounded-lg mb-4">
              <Text className="text-sm">Steps to verify your channel:</Text>
              <Text className="text-sm">1. Go to YouTube Studio</Text>
              <Text className="text-sm">2. Switch to the {ineligibleChannel?.channel_name} channel</Text>
              <Text className="text-sm">3. Visit <Text className="text-blue-600 underline" onPress={() => Linking.openURL('https://www.youtube.com/verify')}>youtube.com/verify</Text></Text>
              <Text className="text-sm">4. Follow the verification steps</Text>
            </View>

            <View className="flex-row justify-end">
              <TouchableOpacity
                onPress={() => setShowLongVideoWarning(false)}
                className="bg-gray-200 px-4 py-2 rounded-lg mr-2"
              >
                <Text className="text-gray-800">Select Different Channel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}

// PropTypes validation

DestinationSelector.propTypes = {
  onComplete: PropTypes.func.isRequired,
  initialDestinations: PropTypes.array,
  initialSelectedLabels: PropTypes.array,
  wizardData: PropTypes.object,
  buttonContainerStyle: PropTypes.object,
};

DestinationSelector.defaultProps = {
  initialDestinations: [],
  initialSelectedLabels: [],
  wizardData: {},
  buttonContainerStyle: null,
};
