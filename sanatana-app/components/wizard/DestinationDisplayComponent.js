import React, { useState, useContext } from "react";
import { View, Text, TextInput, TouchableOpacity, Alert } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";
import { format } from "date-fns";
import axios from "axios";
import Constants from "expo-constants";
import { UserContext } from "../../context/UserContext";

export default function DestinationDisplayComponent({ wizardData }) {
  const { destinations, uploadWithinHour, scheduleDate, scheduleTime } =
    wizardData;
  const { user } = useContext(UserContext);
  const [labelName, setLabelName] = useState("");
  const [saveMessage, setSaveMessage] = useState("");

  // Function to save label
  const handleSaveLabel = async () => {
    if (!labelName.trim()) return;

    try {
      // Check if label already exists
      const checkResponse = await axios.get(
        `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/get_labeled_destinations`,
        {
          params: { sanatana_email: user.email }
        }
      );

      const existingLabels = checkResponse.data || [];
      const labelExists = existingLabels.some(
        label => label.label.toLowerCase() === labelName.trim().toLowerCase()
      );

      if (labelExists) {
        Alert.alert(
          "Label Already Exists",
          `A label named "${labelName.trim()}" already exists. Do you want to update it?`,
          [
            { text: "Cancel", style: "cancel" },
            { text: "Update", onPress: () => saveDestinationLabel() }
          ]
        );
      } else {
        saveDestinationLabel();
      }
    } catch (error) {
      console.error("Error checking existing labels:", error);
      setSaveMessage("Error checking existing labels");
    }
  };

  const saveDestinationLabel = async () => {
    try {
      const response = await axios.post(
        `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/set_labeled_destination`,
        {
          sanatana_email: user.email,
          label: labelName.trim(),
          destination_json: destinations
        }
      );

      if (response.status === 200) {
        setSaveMessage("Label saved successfully");
        setLabelName("");
        // Clear message after 3 seconds
        setTimeout(() => setSaveMessage(""), 3000);
      }
    } catch (error) {
      console.error("Error saving label:", error);
      setSaveMessage("Error saving label");
      setTimeout(() => setSaveMessage(""), 3000);
    }
  };

  // Helper function to format date and time
  const formatDateTime = () => {
    if (!scheduleDate) return null;

    try {
      // Handle different date formats
      let dateObj;

      if (typeof scheduleDate === "string") {
        // If it's a string, parse it
        dateObj = new Date(scheduleDate);
      } else if (scheduleDate instanceof Date) {
        // If it's already a Date object
        dateObj = scheduleDate;
      } else if (typeof scheduleDate === "object" && scheduleDate !== null) {
        // If it's a serialized date object
        dateObj = new Date(scheduleDate);
      } else {
        console.error("Unknown date format:", scheduleDate);
        return "Invalid date format";
      }

      // Check if the date is valid
      if (isNaN(dateObj.getTime())) {
        console.error("Invalid date object:", dateObj);
        return "Invalid date";
      }

      // Format the date
      const dateStr = format(dateObj, "MMMM d, yyyy");

      // Handle time
      let timeStr;
      if (scheduleTime instanceof Date) {
        timeStr = format(scheduleTime, "h:mm a");
      } else if (
        typeof scheduleTime === "string" &&
        scheduleTime.includes(":")
      ) {
        // If it's a time string like "14:30"
        const [hours, minutes] = scheduleTime.split(":").map(Number);
        const timeObj = new Date();
        timeObj.setHours(hours, minutes, 0, 0);
        timeStr = format(timeObj, "h:mm a");
      } else {
        // Use the time from the date object
        timeStr = format(dateObj, "h:mm a");
      }

      return `${dateStr} at ${timeStr}`;
    } catch (error) {
      console.error("Error formatting date:", error, scheduleDate);
      return "Invalid date";
    }
  };

  // Helper function to get playlist name based on type
  const getPlaylistName = (playlist) => {
    if (!playlist) return "None";

    if (playlist.type === "auto") return "Auto Playlist";
    if (playlist.type === "none") return "No Playlist";
    return playlist.name || "None";
  };

  // Helper function to get scheduling information
  const getSchedulingInfo = () => {
    if (uploadWithinHour) {
      return (
        <View className="flex-row items-center">
          <Icon
            name="clock-o"
            type="font-awesome"
            size={16}
            color="#16A34A"
            style={{ marginRight: 8 }}
          />
          <Text className="text-green-600">Upload within 1 hour</Text>
        </View>
      );
    } else if (scheduleDate) {
      const formattedDateTime = formatDateTime();
      if (!formattedDateTime) return null;

      return (
        <View className="flex-row items-center">
          <Icon
            name="calendar"
            type="font-awesome"
            size={16}
            color="#3B82F6"
            style={{ marginRight: 8 }}
          />
          <Text className="text-blue-600">
            Scheduled for {formattedDateTime}
          </Text>
        </View>
      );
    }

    return null;
  };

  return (
    <View className="mb-5 p-4 bg-white rounded-lg border border-gray-200">
      <View className="flex-row items-center mb-3">
        <Icon
          name="share-alt"
          type="font-awesome"
          size={20}
          color="#3B82F6"
          style={{ marginRight: 8 }}
        />
        <Text className="text-lg font-bold text-gray-800">
          Destination & Schedule
        </Text>
      </View>

      {/* Scheduling information */}
      <View className="mb-4">
        {getSchedulingInfo() || (
          <Text className="text-gray-500 italic">
            No scheduling information provided
          </Text>
        )}
      </View>

      {/* Destinations */}
      <Text className="font-medium text-gray-700 mb-2">
        Uploading to {destinations.length} destination
        {destinations.length !== 1 ? "s" : ""}:
      </Text>

      {destinations.map((dest, index) => (
        <View
          key={`${dest.id}-${index}`}
          className="mb-3 pl-2 border-l-2 border-blue-200"
        >
          <Text className="text-gray-700 font-medium">
            {index + 1}. {dest.name || dest.id}
          </Text>

          {/* Channel information */}
          {dest.playlist && dest.playlist.channel_name && (
            <Text className="text-gray-600 ml-2">
              Channel: {dest.playlist.channel_name}
            </Text>
          )}

          {/* Playlist information */}
          {(dest.id === "youtube_video" || dest.id === "youtube_shorts") &&
            dest.playlist && (
              <View className="flex-row items-center ml-2">
                <Icon
                  name="list-ul"
                  type="font-awesome"
                  size={12}
                  color="#4B5563"
                  style={{ marginRight: 4 }}
                />
                <Text className="text-gray-600">
                  Playlist: {getPlaylistName(dest.playlist)}
                </Text>
              </View>
            )}

          {/* Platform User ID information for social media */}
          {(dest.id === "tiktok_video" || dest.id === "facebook_post" || dest.id === "instagram_post") &&
            dest.platform_userid && (
              <View className="flex-row items-center ml-2">
                <Icon
                  name="user"
                  type="font-awesome"
                  size={12}
                  color="#4B5563"
                  style={{ marginRight: 4 }}
                />
                <Text className="text-gray-600">
                  Profile: {dest.platform_userid}
                </Text>
              </View>
            )}
        </View>
      ))}

      {destinations.length === 0 && (
        <Text className="text-red-500">No destinations selected</Text>
      )}

      {/* Label Saving Section */}
      {destinations.length > 0 && (
        <View className="mt-6 pt-4 border-t border-gray-200">
          <Text className="text-sm font-medium text-gray-700 mb-2">
            Destinations Label
          </Text>
          <View className="flex-row items-center space-x-2">
            <TextInput
              value={labelName}
              onChangeText={setLabelName}
              placeholder="Enter label name"
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-gray-800"
              style={{ minHeight: 40 }}
            />
            <TouchableOpacity
              onPress={handleSaveLabel}
              disabled={!labelName.trim()}
              className={`px-4 py-2 rounded-lg ${
                labelName.trim()
                  ? "bg-blue-600"
                  : "bg-gray-300"
              }`}
              style={{ minHeight: 40, justifyContent: 'center' }}
            >
              <Text className={`font-medium ${
                labelName.trim() ? "text-white" : "text-gray-500"
              }`}>
                Save label for future
              </Text>
            </TouchableOpacity>
          </View>
          {saveMessage && (
            <Text className={`mt-2 text-sm ${
              saveMessage.includes("successfully") ? "text-green-600" : "text-red-600"
            }`}>
              {saveMessage}
            </Text>
          )}
        </View>
      )}
    </View>
  );
}

DestinationDisplayComponent.propTypes = {
  wizardData: PropTypes.object.isRequired,
};
