import React, { useContext } from "react";
import {
  View,
  Text,
  Image,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Platform,
} from "react-native";
import { UserContext } from "../context/UserContext";
import Cookies from "js-cookie";
import Constants from "expo-constants";
import PropTypes from "prop-types";
import ProfileJobStatus from "../components/ProfileJobStatus";
import ResponsiveContainer from "../components/layout/ResponsiveContainer";

const windowHeight = Dimensions.get("window").height;
const windowWidth = Dimensions.get("window").width;
const isMobile = Platform.OS === 'android' || Platform.OS === 'ios' || windowWidth < 768;

function ProfileScreen({ navigation }) {
  const { user, setUser } = useContext(UserContext);
  // Different layout for mobile vs desktop

  console.log('ProfileScreen rendered, user:', user, 'isMobile:', isMobile);
  const handleLogout = () => {
    Cookies.remove("sanatana_signed_in_email");
    setUser(null);
    window.location.href = `${Constants.expoConfig.extra.SANATANA_SIGNIN_DOMAIN}`;
  };

  return (
    <SafeAreaView style={{ flex: 1 }} className="bg-gray-100">
      <ScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        className="flex-1"
      >
        <ResponsiveContainer>
          <View className="p-5 items-center">
          {/* Header with logout */}
          {user && (<>
            <View className="w-full flex-row justify-end items-center mb-8">
              <TouchableOpacity
                onPress={handleLogout}
                className="bg-red-500 px-4 py-2 rounded-lg"
              >
                <Text className="text-white">Logout</Text>
              </TouchableOpacity>
            </View>

            <View className="w-full max-w-md">
              {/* Account Info Card */}
              <View className="bg-white rounded-lg shadow p-6 mb-4">
                <Text className="text-xl font-semibold mb-4">Account Info</Text>
                <View className="items-center mb-8">
                  <Image
                    source={{ uri: `${user.profile_picture}` }}
                    className="w-20 h-20 rounded-full mb-3"
                  />
                  <Text className="text-2xl font-bold text-gray-800">
                    {user.name}
                  </Text>
                  <Text className="text-gray-600">{user.email}</Text>
                </View>
              </View>

              {/* For mobile, show a button to navigate to job status screen */}
              {/* {isMobile && ( */}
                <View className="mb-4">
                  <TouchableOpacity
                    className="bg-indigo-600 py-3 px-4 rounded-lg w-full items-center"
                    onPress={() => navigation.navigate('JobStatus')}
                  >
                    <Text className="text-white font-semibold">View Upload Jobs</Text>
                  </TouchableOpacity>
                </View>
             
            </View>
          </>)}
          </View>
        </ResponsiveContainer>
      </ScrollView>
    </SafeAreaView>
  );
}

ProfileScreen.propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func.isRequired
  }).isRequired
};

export default ProfileScreen;
