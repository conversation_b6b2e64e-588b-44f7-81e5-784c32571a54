import os
import argparse
import subprocess
import json
from flask import Flask, request, jsonify
import sys

# Import the endpoint methods from their respective files
from upload_video import upload_video_method
from add_to_playlist import add_to_playlist_method
from change_visibility import change_visibility_method
from prepend_description import prepend_description_method
from update_playlist_links import update_playlist_links_method
from reorder_playlist import reorder_playlist_method
from long_uploads import get_long_video_status
from dbutils import get_channel_email

app = Flask(__name__)

# Helper function to run a subprocess command and return JSON response
def run_command(command):
    """Runs a subprocess command and ensures the correct virtual environment is used."""
    print(f"Running command: {' '.join(command)}")
    print(f"Using Python interpreter: {sys.executable}")

    # Ensure the correct virtual environment is used
    if "VIRTUAL_ENV" in os.environ:
        venv_python = os.path.join(os.environ["VIRTUAL_ENV"], "Scripts", "python") if os.name == "nt" else os.path.join(os.environ["VIRTUAL_ENV"], "bin", "python")
    else:
        venv_python = sys.executable  # Use current Python executable as fallback

    # Ensure command array is structured correctly
    command[0] = venv_python

    print(f"Final command: {' '.join(command)}")

    try:
        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=True, env=os.environ.copy())
        return {"status": "SUCCESS", "output": result.stdout.strip()}
    except subprocess.CalledProcessError as e:
        return {"status": "ERROR", "error": e.stderr.strip()}

# Specialized version of run_command for upload_video that extracts video ID
def upload_video_subprocess(command):
    """Runs upload_video.py and extracts the video ID from the output."""
    # First run the command using the standard run_command function
    response = run_command(command)

    # If successful, extract the video ID
    if response["status"] == "SUCCESS":
        for line in response["output"].split("\n"):
            if line.startswith("Video id:"):  # Find the correct line
                parts = line.split(":")  # Split at ":"
                if len(parts) > 1:
                    video_part = parts[1].split("-")[0].strip()  # Extract ID before " -"
                    response["video_id"] = video_part
                    response["youtube_link"] = f"https://www.youtube.com/watch?v={video_part}"
                    break  # Stop after finding the first match

    return response

# Upload Video
@app.route('/hello', methods=['GET'])
def hello():
    return jsonify({"status":"success", "message":"Hello, how are you doing?"})


# Upload Video
@app.route('/long_video_status', methods=['POST'])
def long_video_status():
    data = request.json

    # Call the direct method instead of using subprocess
    result = get_long_video_status(
        email=get_channel_email(data.get("email", None), data.get("channel_id", None)),
    )
    return jsonify(result)

# Upload Video
@app.route('/upload_video', methods=['POST'])
def upload_video():
    data = request.json

    # Call the direct method instead of using subprocess
    result = upload_video_method(
        file=data["file"],
        title=data["title"][:70],  # Ensure title is within 70 chars
        description=data["description"],
        keywords=data["keywords"],
        category=data["category"],
        privacyStatus=data["privacyStatus"],
        email=get_channel_email(data.get("email", None), data.get("channel_id", None)),
    )

    return jsonify(result)

# Add Video to Playlist
@app.route('/add_to_playlist', methods=['POST'])
def add_to_playlist():
    data = request.json

    # Call the direct method
    result = add_to_playlist_method(
        video_id=data["video_id"],
        playlist=data["playlist"],
        email=get_channel_email(data.get("email", None), data.get("channel_id", None)),
    )

    return jsonify(result)

# Original implementation using subprocess
def add_to_playlist_subprocess():
    data = request.json
    command = ["python", "add_to_playlist.py", "--video_id", data["video_id"], "--playlist", data["playlist"], "--email", data["email"]]

    response = run_command(command)

    if response["status"] == "SUCCESS":
        playlist_id = response["output"].split()[-1]  # Assuming playlist ID is last output
        response["playlist_id"] = f"{playlist_id}"

    return jsonify(response)

# Change Video Visibility
@app.route('/change_visibility', methods=['POST'])
def change_visibility():
    data = request.json

    # Call the direct method
    result = change_visibility_method(
        video_id=data["video_id"],
        privacy_status=data["privacy_status"],
        email=get_channel_email(data.get("email", None), data.get("channel_id", None)),
    )

    return jsonify(result)

# Original implementation using subprocess
def change_visibility_subprocess():
    data = request.json
    command = ["python", "change_visibility.py", "--video_id", data["video_id"], "--privacy_status", data["privacy_status"], "--email", data["email"]]

    response = run_command(command)

    if response["status"] == "SUCCESS":
        response["youtube_link"] = f"https://www.youtube.com/watch?v={data['video_id']}"

    return jsonify(response)

# Prepend Description to Video
@app.route('/prepend_description', methods=['POST'])
def prepend_description():
    data = request.json

    # Call the direct method
    result = prepend_description_method(
        video_id=data["video_id"],
        text=data["text"],
        email=get_channel_email(data.get("email", None), data.get("channel_id", None)),
    )

    return jsonify(result)

# Original implementation using subprocess
def prepend_description_subprocess():
    data = request.json
    command = ["python", "prepend_description.py", "--video_id", data["video_id"], "--text", data["text"], "--email", data["email"]]

    response = run_command(command)

    if response["status"] == "SUCCESS":
        response["youtube_link"] = f"https://www.youtube.com/watch?v={data['video_id']}"

    return jsonify(response)

# Update Playlist Links
@app.route('/update_playlist_links', methods=['POST'])
def update_playlist_links():
    data = request.json

    # Call the direct method
    result = update_playlist_links_method(
        playlist_id=data["playlist_id"],
        email=get_channel_email(data.get("email", None), data.get("channel_id", None)),
    )

    # Add playlist link if not already present
    if result["status"] == "SUCCESS" and "playlist_link" not in result:
        result["playlist_link"] = f"https://www.youtube.com/playlist?list={data['playlist_id']}"

    return jsonify(result)

# Original implementation using subprocess
def update_playlist_links_subprocess():
    data = request.json
    command = ["python", "update_playlist_links.py", "--playlist_id", data["playlist_id"], "--email", data["email"]]

    response = run_command(command)

    if response["status"] == "SUCCESS":
        response["playlist_link"] = f"https://www.youtube.com/playlist?list={data['playlist_id']}"

    return jsonify(response)

# Reorder Playlist
@app.route('/reorder_playlist', methods=['POST'])
def reorder_playlist():
    data = request.json

    # Call the direct method
    result = reorder_playlist_method(
        playlist_id=data["playlist_id"],
        sort_by=data["sort_by"],
        email=get_channel_email(data.get("email", None), data.get("channel_id", None)),
    )

    # Add playlist link if not already present
    if result["status"] == "SUCCESS" and "playlist_link" not in result:
        result["playlist_link"] = f"https://www.youtube.com/playlist?list={data['playlist_id']}"

    return jsonify(result)

# Original implementation using subprocess
def reorder_playlist_subprocess():
    data = request.json
    command = ["python", "reorder_playlist.py", "--playlist_id", data["playlist_id"], "--sort_by", data["sort_by"], "--email", data["email"]]

    response = run_command(command)

    if response["status"] == "SUCCESS":
        response["playlist_link"] = f"https://www.youtube.com/playlist?list={data['playlist_id']}"

    return jsonify(response)

def main():
    # Service name
    service_name = "YOUTUBE_SERVICE"

    # Parse command-line arguments for port
    parser = argparse.ArgumentParser(description=f"Start the {service_name} microservice.")
    parser.add_argument('--port', type=int, help="Port to run the service on")
    args = parser.parse_args()

    # Determine the port: Use CLI argument > Environment variable > Default
    PORT = args.port or int(os.getenv(service_name, 5001))

    # Get the parent directory of the current script
    parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

    # Add it to sys.path
    sys.path.append(parent_dir)

    from Utils.globals import save_port_in_global_ports

    # Write back to global ports file
    save_port_in_global_ports(service_name, PORT)

    print(f"Starting service {service_name} on port {PORT}...")
    app.run(host="0.0.0.0", port=PORT, threaded=True)


if __name__ == '__main__':
    main()
    ...
    """
        usage:
        python youtube_service.py --port 5001

        - If the port is specified in command line it will use that port
        - If not, it will look for environment variable, YOUTUBE_SERVICE,
        - Even if that is not found, then it will assign default port to 5001
        - Saves port to global ports file under key YOUTUBE_SERVICE, so local client applications can connect

    """