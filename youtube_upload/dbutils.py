import os
import sqlite3
import sys
import json
import sqlite3
import requests

# Get the parent directory of the current script
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

# Add it to sys.path
sys.path.append(parent_dir)

from Utils.globals import determine_domains_file
GLOBAL_DOMAINS_FILE = determine_domains_file()
SANATANA_SERVICE_DOMAIN = None


try:
    global_config_path = os.path.join(os.path.dirname(__file__), '..', GLOBAL_DOMAINS_FILE)
    with open(global_config_path, 'r') as f:
        global_config = json.load(f)
      
        SANATANA_SERVICE_DOMAIN = global_config.get("SANATANA_SERVICE_DOMAIN", "")

except Exception as e:
    print(f"Failed to load configuration: {str(e)}")
    raise


def get_channel_email(email, channel_id):
    print(f"get_channel_email: email: {email}; channel_id: {channel_id}")
    if email:
        return email
    
    return get_channel_email_by_channel_id(channel_id)


def get_channel_email_by_channel_id(channel_id):
    try:
        print(f"channel_id: {channel_id}; SANATANA_SERVICE_DOMAIN: {SANATANA_SERVICE_DOMAIN}")
        url = f"{SANATANA_SERVICE_DOMAIN}/youtube/get_channel_email?channel_id={channel_id}"
       
        response = requests.get(url)

        if response.status_code == 200:
            return response.text 
        else:
            print(f"get_channel_email_by_channel_id: Failed with status {response.status_code}")
            return {"error": f"Failed to retrieve email, status code {response.status_code}"}

    except Exception as e:
        print(f"get_channel_email_by_channel_id: Exception occurred: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    email = get_channel_email_by_channel_id("UCV56iYuE1V-3VK84SOBFbIg")
    print(f"email = {email}")